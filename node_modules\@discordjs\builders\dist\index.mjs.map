{"version": 3, "sources": ["../src/messages/embed/Assertions.ts", "../src/util/validation.ts", "../src/util/normalizeArray.ts", "../src/messages/embed/Embed.ts", "../src/index.ts", "../src/components/Assertions.ts", "../src/components/selectMenu/StringSelectMenuOption.ts", "../src/components/ActionRow.ts", "../src/components/Component.ts", "../src/components/Components.ts", "../src/components/button/Button.ts", "../src/components/selectMenu/ChannelSelectMenu.ts", "../src/components/selectMenu/BaseSelectMenu.ts", "../src/components/selectMenu/MentionableSelectMenu.ts", "../src/components/selectMenu/RoleSelectMenu.ts", "../src/components/selectMenu/StringSelectMenu.ts", "../src/components/selectMenu/UserSelectMenu.ts", "../src/components/textInput/TextInput.ts", "../src/components/textInput/Assertions.ts", "../src/components/v2/Container.ts", "../src/components/v2/Assertions.ts", "../src/components/v2/Thumbnail.ts", "../src/components/v2/File.ts", "../src/components/v2/Separator.ts", "../src/components/v2/TextDisplay.ts", "../src/components/v2/MediaGallery.ts", "../src/components/v2/MediaGalleryItem.ts", "../src/components/v2/Section.ts", "../src/interactions/modals/Assertions.ts", "../src/interactions/modals/Modal.ts", "../src/interactions/slashCommands/Assertions.ts", "../src/interactions/slashCommands/SlashCommandBuilder.ts", "../src/interactions/slashCommands/mixins/NameAndDescription.ts", "../src/interactions/slashCommands/mixins/SharedSlashCommand.ts", "../src/interactions/slashCommands/options/attachment.ts", "../src/interactions/slashCommands/mixins/ApplicationCommandOptionBase.ts", "../src/interactions/slashCommands/options/boolean.ts", "../src/interactions/slashCommands/options/channel.ts", "../src/interactions/slashCommands/mixins/ApplicationCommandOptionChannelTypesMixin.ts", "../src/interactions/slashCommands/options/integer.ts", "../src/interactions/slashCommands/mixins/ApplicationCommandNumericOptionMinMaxValueMixin.ts", "../src/interactions/slashCommands/mixins/ApplicationCommandOptionWithAutocompleteMixin.ts", "../src/interactions/slashCommands/mixins/ApplicationCommandOptionWithChoicesMixin.ts", "../src/interactions/slashCommands/options/mentionable.ts", "../src/interactions/slashCommands/options/number.ts", "../src/interactions/slashCommands/options/role.ts", "../src/interactions/slashCommands/options/string.ts", "../src/interactions/slashCommands/options/user.ts", "../src/interactions/slashCommands/mixins/SharedSlashCommandOptions.ts", "../src/interactions/slashCommands/SlashCommandSubcommands.ts", "../src/interactions/slashCommands/mixins/SharedSubcommands.ts", "../src/interactions/contextMenuCommands/Assertions.ts", "../src/interactions/contextMenuCommands/ContextMenuCommandBuilder.ts", "../src/util/componentUtil.ts"], "sourcesContent": ["import { s } from '@sapphire/shapeshift';\nimport type { APIEmbedField } from 'discord-api-types/v10';\nimport { isValidationEnabled } from '../../util/validation.js';\n\nexport const fieldNamePredicate = s.string().lengthLessThanOrEqual(256).setValidationEnabled(isValidationEnabled);\n\nexport const fieldValuePredicate = s.string().lengthLessThanOrEqual(1_024).setValidationEnabled(isValidationEnabled);\n\nexport const fieldInlinePredicate = s.boolean().optional();\n\nexport const embedFieldPredicate = s\n\t.object({\n\t\tname: fieldNamePredicate,\n\t\tvalue: fieldValuePredicate,\n\t\tinline: fieldInlinePredicate,\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const embedFieldsArrayPredicate = embedFieldPredicate.array().setValidationEnabled(isValidationEnabled);\n\nexport const fieldLengthPredicate = s.number().lessThanOrEqual(25).setValidationEnabled(isValidationEnabled);\n\nexport function validateFieldLength(amountAdding: number, fields?: APIEmbedField[]): void {\n\tfieldLengthPredicate.parse((fields?.length ?? 0) + amountAdding);\n}\n\nexport const authorNamePredicate = fieldNamePredicate\n\t.lengthGreaterThanOrEqual(1)\n\t.nullable()\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const imageURLPredicate = s\n\t.string()\n\t.url({\n\t\tallowedProtocols: ['http:', 'https:', 'attachment:'],\n\t})\n\t.nullish()\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const urlPredicate = s\n\t.string()\n\t.url({\n\t\tallowedProtocols: ['http:', 'https:'],\n\t})\n\t.nullish()\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const embedAuthorPredicate = s\n\t.object({\n\t\tname: authorNamePredicate,\n\t\ticonURL: imageURLPredicate,\n\t\turl: urlPredicate,\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const RGBPredicate = s\n\t.number()\n\t.int()\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(255)\n\t.setValidationEnabled(isValidationEnabled);\nexport const colorPredicate = s\n\t.number()\n\t.int()\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(0xffffff)\n\t.or(s.tuple([RGBPredicate, RGBPredicate, RGBPredicate]))\n\t.nullable()\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const descriptionPredicate = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(4_096)\n\t.nullable()\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const footerTextPredicate = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(2_048)\n\t.nullable()\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const embedFooterPredicate = s\n\t.object({\n\t\ttext: footerTextPredicate,\n\t\ticonURL: imageURLPredicate,\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const timestampPredicate = s.union([s.number(), s.date()]).nullable().setValidationEnabled(isValidationEnabled);\n\nexport const titlePredicate = fieldNamePredicate\n\t.lengthGreaterThanOrEqual(1)\n\t.nullable()\n\t.setValidationEnabled(isValidationEnabled);\n", "let validate = true;\n\n/**\n * Enables validators.\n *\n * @returns Whether validation is occurring.\n */\nexport function enableValidators() {\n\treturn (validate = true);\n}\n\n/**\n * Disables validators.\n *\n * @returns Whether validation is occurring.\n */\nexport function disableValidators() {\n\treturn (validate = false);\n}\n\n/**\n * Checks whether validation is occurring.\n */\nexport function isValidationEnabled() {\n\treturn validate;\n}\n", "/**\n * Normalizes data that is a rest parameter or an array into an array with a depth of 1.\n *\n * @typeParam ItemType - The data that must satisfy {@link RestOrArray}.\n * @param arr - The (possibly variadic) data to normalize\n */\nexport function normalizeArray<ItemType>(arr: RestOrArray<ItemType>): ItemType[] {\n\tif (Array.isArray(arr[0])) return [...arr[0]];\n\treturn arr as ItemType[];\n}\n\n/**\n * Represents data that may be an array or came from a rest parameter.\n *\n * @remarks\n * This type is used throughout builders to ensure both an array and variadic arguments\n * may be used. It is normalized with {@link normalizeArray}.\n */\nexport type RestOrArray<Type> = Type[] | [Type[]];\n", "import type { APIEmbed, APIEmbedAuthor, APIEmbedField, APIEmbedFooter, APIEmbedImage } from 'discord-api-types/v10';\nimport { normalizeArray, type RestOrArray } from '../../util/normalizeArray.js';\nimport {\n\tcolorPredicate,\n\tdescriptionPredicate,\n\tembedAuthorPredicate,\n\tembedFieldsArrayPredicate,\n\tembedFooterPredicate,\n\timageURLPredicate,\n\ttimestampPredicate,\n\ttitlePredicate,\n\turlPredicate,\n\tvalidateFieldLength,\n} from './Assertions.js';\n\n/**\n * A tuple satisfying the RGB color model.\n *\n * @see {@link https://developer.mozilla.org/docs/Glossary/RGB}\n */\nexport type RGBTuple = [red: number, green: number, blue: number];\n\n/**\n * The base icon data typically used in payloads.\n */\nexport interface IconData {\n\t/**\n\t * The URL of the icon.\n\t */\n\ticonURL?: string;\n\t/**\n\t * The proxy URL of the icon.\n\t */\n\tproxyIconURL?: string;\n}\n\n/**\n * Represents the author data of an embed.\n */\nexport interface EmbedAuthorData extends IconData, Omit<APIEmbedAuthor, 'icon_url' | 'proxy_icon_url'> {}\n\n/**\n * Represents the author options of an embed.\n */\nexport interface EmbedAuthorOptions extends Omit<EmbedAuthorData, 'proxyIconURL'> {}\n\n/**\n * Represents the footer data of an embed.\n */\nexport interface EmbedFooterData extends IconData, Omit<APIEmbedFooter, 'icon_url' | 'proxy_icon_url'> {}\n\n/**\n * Represents the footer options of an embed.\n */\nexport interface EmbedFooterOptions extends Omit<EmbedFooterData, 'proxyIconURL'> {}\n\n/**\n * Represents the image data of an embed.\n */\nexport interface EmbedImageData extends Omit<APIEmbedImage, 'proxy_url'> {\n\t/**\n\t * The proxy URL for the image.\n\t */\n\tproxyURL?: string;\n}\n\n/**\n * A builder that creates API-compatible JSON data for embeds.\n */\nexport class EmbedBuilder {\n\t/**\n\t * The API data associated with this embed.\n\t */\n\tpublic readonly data: APIEmbed;\n\n\t/**\n\t * Creates a new embed from API data.\n\t *\n\t * @param data - The API data to create this embed with\n\t */\n\tpublic constructor(data: APIEmbed = {}) {\n\t\tthis.data = { ...data };\n\t\tif (data.timestamp) this.data.timestamp = new Date(data.timestamp).toISOString();\n\t}\n\n\t/**\n\t * Appends fields to the embed.\n\t *\n\t * @remarks\n\t * This method accepts either an array of fields or a variable number of field parameters.\n\t * The maximum amount of fields that can be added is 25.\n\t * @example\n\t * Using an array:\n\t * ```ts\n\t * const fields: APIEmbedField[] = ...;\n\t * const embed = new EmbedBuilder()\n\t * \t.addFields(fields);\n\t * ```\n\t * @example\n\t * Using rest parameters (variadic):\n\t * ```ts\n\t * const embed = new EmbedBuilder()\n\t * \t.addFields(\n\t * \t\t{ name: 'Field 1', value: 'Value 1' },\n\t * \t\t{ name: 'Field 2', value: 'Value 2' },\n\t * \t);\n\t * ```\n\t * @param fields - The fields to add\n\t */\n\tpublic addFields(...fields: RestOrArray<APIEmbedField>): this {\n\t\tconst normalizedFields = normalizeArray(fields);\n\t\t// Ensure adding these fields won't exceed the 25 field limit\n\t\tvalidateFieldLength(normalizedFields.length, this.data.fields);\n\n\t\t// Data assertions\n\t\tembedFieldsArrayPredicate.parse(normalizedFields);\n\n\t\tif (this.data.fields) this.data.fields.push(...normalizedFields);\n\t\telse this.data.fields = normalizedFields;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Removes, replaces, or inserts fields for this embed.\n\t *\n\t * @remarks\n\t * This method behaves similarly\n\t * to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/splice | Array.prototype.splice()}.\n\t * The maximum amount of fields that can be added is 25.\n\t *\n\t * It's useful for modifying and adjusting order of the already-existing fields of an embed.\n\t * @example\n\t * Remove the first field:\n\t * ```ts\n\t * embed.spliceFields(0, 1);\n\t * ```\n\t * @example\n\t * Remove the first n fields:\n\t * ```ts\n\t * const n = 4;\n\t * embed.spliceFields(0, n);\n\t * ```\n\t * @example\n\t * Remove the last field:\n\t * ```ts\n\t * embed.spliceFields(-1, 1);\n\t * ```\n\t * @param index - The index to start at\n\t * @param deleteCount - The number of fields to remove\n\t * @param fields - The replacing field objects\n\t */\n\tpublic spliceFields(index: number, deleteCount: number, ...fields: APIEmbedField[]): this {\n\t\t// Ensure adding these fields won't exceed the 25 field limit\n\t\tvalidateFieldLength(fields.length - deleteCount, this.data.fields);\n\n\t\t// Data assertions\n\t\tembedFieldsArrayPredicate.parse(fields);\n\t\tif (this.data.fields) this.data.fields.splice(index, deleteCount, ...fields);\n\t\telse this.data.fields = fields;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the fields for this embed.\n\t *\n\t * @remarks\n\t * This method is an alias for {@link EmbedBuilder.spliceFields}. More specifically,\n\t * it splices the entire array of fields, replacing them with the provided fields.\n\t *\n\t * You can set a maximum of 25 fields.\n\t * @param fields - The fields to set\n\t */\n\tpublic setFields(...fields: RestOrArray<APIEmbedField>): this {\n\t\tthis.spliceFields(0, this.data.fields?.length ?? 0, ...normalizeArray(fields));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the author of this embed.\n\t *\n\t * @param options - The options to use\n\t */\n\n\tpublic setAuthor(options: EmbedAuthorOptions | null): this {\n\t\tif (options === null) {\n\t\t\tthis.data.author = undefined;\n\t\t\treturn this;\n\t\t}\n\n\t\t// Data assertions\n\t\tembedAuthorPredicate.parse(options);\n\n\t\tthis.data.author = { name: options.name, url: options.url, icon_url: options.iconURL };\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the color of this embed.\n\t *\n\t * @param color - The color to use\n\t */\n\tpublic setColor(color: RGBTuple | number | null): this {\n\t\t// Data assertions\n\t\tcolorPredicate.parse(color);\n\n\t\tif (Array.isArray(color)) {\n\t\t\tconst [red, green, blue] = color;\n\t\t\tthis.data.color = (red << 16) + (green << 8) + blue;\n\t\t\treturn this;\n\t\t}\n\n\t\tthis.data.color = color ?? undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the description of this embed.\n\t *\n\t * @param description - The description to use\n\t */\n\tpublic setDescription(description: string | null): this {\n\t\t// Data assertions\n\t\tdescriptionPredicate.parse(description);\n\n\t\tthis.data.description = description ?? undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the footer of this embed.\n\t *\n\t * @param options - The footer to use\n\t */\n\tpublic setFooter(options: EmbedFooterOptions | null): this {\n\t\tif (options === null) {\n\t\t\tthis.data.footer = undefined;\n\t\t\treturn this;\n\t\t}\n\n\t\t// Data assertions\n\t\tembedFooterPredicate.parse(options);\n\n\t\tthis.data.footer = { text: options.text, icon_url: options.iconURL };\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the image of this embed.\n\t *\n\t * @param url - The image URL to use\n\t */\n\tpublic setImage(url: string | null): this {\n\t\t// Data assertions\n\t\timageURLPredicate.parse(url);\n\n\t\tthis.data.image = url ? { url } : undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the thumbnail of this embed.\n\t *\n\t * @param url - The thumbnail URL to use\n\t */\n\tpublic setThumbnail(url: string | null): this {\n\t\t// Data assertions\n\t\timageURLPredicate.parse(url);\n\n\t\tthis.data.thumbnail = url ? { url } : undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the timestamp of this embed.\n\t *\n\t * @param timestamp - The timestamp or date to use\n\t */\n\tpublic setTimestamp(timestamp: Date | number | null = Date.now()): this {\n\t\t// Data assertions\n\t\ttimestampPredicate.parse(timestamp);\n\n\t\tthis.data.timestamp = timestamp ? new Date(timestamp).toISOString() : undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the title for this embed.\n\t *\n\t * @param title - The title to use\n\t */\n\tpublic setTitle(title: string | null): this {\n\t\t// Data assertions\n\t\ttitlePredicate.parse(title);\n\n\t\tthis.data.title = title ?? undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the URL of this embed.\n\t *\n\t * @param url - The URL to use\n\t */\n\tpublic setURL(url: string | null): this {\n\t\t// Data assertions\n\t\turlPredicate.parse(url);\n\n\t\tthis.data.url = url ?? undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Serializes this builder to API-compatible JSON data.\n\t *\n\t * @remarks\n\t * This method runs validations on the data before serializing it.\n\t * As such, it may throw an error if the data is invalid.\n\t */\n\tpublic toJSON(): APIEmbed {\n\t\treturn { ...this.data };\n\t}\n}\n", "export * as EmbedAssertions from './messages/embed/Assertions.js';\nexport * from './messages/embed/Embed.js';\n// TODO: Consider removing this dep in the next major version\nexport * from '@discordjs/formatters';\n\nexport * as ComponentAssertions from './components/Assertions.js';\nexport * from './components/ActionRow.js';\nexport * from './components/button/Button.js';\nexport * from './components/Component.js';\nexport * from './components/Components.js';\nexport * from './components/textInput/TextInput.js';\nexport * as TextInputAssertions from './components/textInput/Assertions.js';\nexport * from './interactions/modals/Modal.js';\nexport * as ModalAssertions from './interactions/modals/Assertions.js';\n\nexport * from './components/selectMenu/BaseSelectMenu.js';\nexport * from './components/selectMenu/ChannelSelectMenu.js';\nexport * from './components/selectMenu/MentionableSelectMenu.js';\nexport * from './components/selectMenu/RoleSelectMenu.js';\nexport * from './components/selectMenu/StringSelectMenu.js';\n// TODO: Remove those aliases in v2\nexport {\n\t/**\n\t * @deprecated Will be removed in the next major version, use {@link StringSelectMenuBuilder} instead.\n\t */\n\tStringSelectMenuBuilder as SelectMenuBuilder,\n} from './components/selectMenu/StringSelectMenu.js';\nexport {\n\t/**\n\t * @deprecated Will be removed in the next major version, use {@link StringSelectMenuOptionBuilder} instead.\n\t */\n\tStringSelectMenuOptionBuilder as SelectMenuOptionBuilder,\n} from './components/selectMenu/StringSelectMenuOption.js';\nexport * from './components/selectMenu/StringSelectMenuOption.js';\nexport * from './components/selectMenu/UserSelectMenu.js';\n\nexport * as ComponentsV2Assertions from './components/v2/Assertions.js';\nexport * from './components/v2/Container.js';\nexport * from './components/v2/File.js';\nexport * from './components/v2/MediaGallery.js';\nexport * from './components/v2/MediaGalleryItem.js';\nexport * from './components/v2/Section.js';\nexport * from './components/v2/Separator.js';\nexport * from './components/v2/TextDisplay.js';\nexport * from './components/v2/Thumbnail.js';\n\nexport * as SlashCommandAssertions from './interactions/slashCommands/Assertions.js';\nexport * from './interactions/slashCommands/SlashCommandBuilder.js';\nexport * from './interactions/slashCommands/SlashCommandSubcommands.js';\nexport * from './interactions/slashCommands/options/boolean.js';\nexport * from './interactions/slashCommands/options/channel.js';\nexport * from './interactions/slashCommands/options/integer.js';\nexport * from './interactions/slashCommands/options/mentionable.js';\nexport * from './interactions/slashCommands/options/number.js';\nexport * from './interactions/slashCommands/options/role.js';\nexport * from './interactions/slashCommands/options/attachment.js';\nexport * from './interactions/slashCommands/options/string.js';\nexport * from './interactions/slashCommands/options/user.js';\nexport * from './interactions/slashCommands/mixins/ApplicationCommandNumericOptionMinMaxValueMixin.js';\nexport * from './interactions/slashCommands/mixins/ApplicationCommandOptionBase.js';\nexport * from './interactions/slashCommands/mixins/ApplicationCommandOptionChannelTypesMixin.js';\nexport * from './interactions/slashCommands/mixins/ApplicationCommandOptionWithAutocompleteMixin.js';\nexport * from './interactions/slashCommands/mixins/ApplicationCommandOptionWithChoicesMixin.js';\nexport * from './interactions/slashCommands/mixins/NameAndDescription.js';\nexport * from './interactions/slashCommands/mixins/SharedSlashCommandOptions.js';\nexport * from './interactions/slashCommands/mixins/SharedSubcommands.js';\nexport * from './interactions/slashCommands/mixins/SharedSlashCommand.js';\n\nexport * as ContextMenuCommandAssertions from './interactions/contextMenuCommands/Assertions.js';\nexport * from './interactions/contextMenuCommands/ContextMenuCommandBuilder.js';\n\nexport * from './util/componentUtil.js';\nexport * from './util/normalizeArray.js';\nexport * from './util/validation.js';\n\n/**\n * The {@link https://github.com/discordjs/discord.js/blob/main/packages/builders#readme | @discordjs/builders} version\n * that you are currently using.\n *\n * @privateRemarks This needs to explicitly be `string` so it is not typed as a \"const string\" that gets injected by esbuild.\n */\nexport const version = '1.11.3' as string;\n", "import { s } from '@sapphire/shapeshift';\nimport { ButtonStyle, ChannelType, type APIMessageComponentEmoji } from 'discord-api-types/v10';\nimport { isValidationEnabled } from '../util/validation.js';\nimport { StringSelectMenuOptionBuilder } from './selectMenu/StringSelectMenuOption.js';\n\nexport const idValidator = s\n\t.number()\n\t.safeInt()\n\t.greaterThanOrEqual(1)\n\t.lessThan(4_294_967_296) // 2^32 - 1\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const customIdValidator = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(100)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const emojiValidator = s\n\t.object({\n\t\tid: s.string(),\n\t\tname: s.string(),\n\t\tanimated: s.boolean(),\n\t})\n\t.partial()\n\t.strict()\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const disabledValidator = s.boolean();\n\nexport const buttonLabelValidator = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(80)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const buttonStyleValidator = s.nativeEnum(ButtonStyle);\n\nexport const placeholderValidator = s.string().lengthLessThanOrEqual(150).setValidationEnabled(isValidationEnabled);\nexport const minMaxValidator = s\n\t.number()\n\t.int()\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(25)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const labelValueDescriptionValidator = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(100)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const jsonOptionValidator = s\n\t.object({\n\t\tlabel: labelValueDescriptionValidator,\n\t\tvalue: labelValueDescriptionValidator,\n\t\tdescription: labelValueDescriptionValidator.optional(),\n\t\temoji: emojiValidator.optional(),\n\t\tdefault: s.boolean().optional(),\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const optionValidator = s.instance(StringSelectMenuOptionBuilder).setValidationEnabled(isValidationEnabled);\n\nexport const optionsValidator = optionValidator\n\t.array()\n\t.lengthGreaterThanOrEqual(0)\n\t.setValidationEnabled(isValidationEnabled);\nexport const optionsLengthValidator = s\n\t.number()\n\t.int()\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(25)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateRequiredSelectMenuParameters(options: StringSelectMenuOptionBuilder[], customId?: string) {\n\tcustomIdValidator.parse(customId);\n\toptionsValidator.parse(options);\n}\n\nexport const defaultValidator = s.boolean();\n\nexport function validateRequiredSelectMenuOptionParameters(label?: string, value?: string) {\n\tlabelValueDescriptionValidator.parse(label);\n\tlabelValueDescriptionValidator.parse(value);\n}\n\nexport const channelTypesValidator = s.nativeEnum(ChannelType).array().setValidationEnabled(isValidationEnabled);\n\nexport const urlValidator = s\n\t.string()\n\t.url({\n\t\tallowedProtocols: ['http:', 'https:', 'discord:'],\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateRequiredButtonParameters(\n\tstyle?: ButtonStyle,\n\tlabel?: string,\n\temoji?: APIMessageComponentEmoji,\n\tcustomId?: string,\n\tskuId?: string,\n\turl?: string,\n) {\n\tif (style === ButtonStyle.Premium) {\n\t\tif (!skuId) {\n\t\t\tthrow new RangeError('Premium buttons must have an SKU id.');\n\t\t}\n\n\t\tif (customId || label || url || emoji) {\n\t\t\tthrow new RangeError('Premium buttons cannot have a custom id, label, URL, or emoji.');\n\t\t}\n\t} else {\n\t\tif (skuId) {\n\t\t\tthrow new RangeError('Non-premium buttons must not have an SKU id.');\n\t\t}\n\n\t\tif (url && customId) {\n\t\t\tthrow new RangeError('URL and custom id are mutually exclusive.');\n\t\t}\n\n\t\tif (!label && !emoji) {\n\t\t\tthrow new RangeError('Non-premium buttons must have a label and/or an emoji.');\n\t\t}\n\n\t\tif (style === ButtonStyle.Link) {\n\t\t\tif (!url) {\n\t\t\t\tthrow new RangeError('Link buttons must have a URL.');\n\t\t\t}\n\t\t} else if (url) {\n\t\t\tthrow new RangeError('Non-premium and non-link buttons cannot have a URL.');\n\t\t}\n\t}\n}\n", "import type { JSONEncodable } from '@discordjs/util';\nimport type { APIMessageComponentEmoji, APISelectMenuOption } from 'discord-api-types/v10';\nimport {\n\tdefaultValidator,\n\temojiValidator,\n\tlabelValueDescriptionValidator,\n\tvalidateRequiredSelectMenuOptionParameters,\n} from '../Assertions.js';\n\n/**\n * A builder that creates API-compatible JSON data for string select menu options.\n */\nexport class StringSelectMenuOptionBuilder implements JSONEncodable<APISelectMenuOption> {\n\t/**\n\t * Creates a new string select menu option from API data.\n\t *\n\t * @param data - The API data to create this string select menu option with\n\t * @example\n\t * Creating a string select menu option from an API data object:\n\t * ```ts\n\t * const selectMenuOption = new SelectMenuOptionBuilder({\n\t * \tlabel: 'catchy label',\n\t * \tvalue: '1',\n\t * });\n\t * ```\n\t * @example\n\t * Creating a string select menu option using setters and API data:\n\t * ```ts\n\t * const selectMenuOption = new SelectMenuOptionBuilder({\n\t * \tdefault: true,\n\t * \tvalue: '1',\n\t * })\n\t * \t.setLabel('woah');\n\t * ```\n\t */\n\tpublic constructor(public data: Partial<APISelectMenuOption> = {}) {}\n\n\t/**\n\t * Sets the label for this option.\n\t *\n\t * @param label - The label to use\n\t */\n\tpublic setLabel(label: string) {\n\t\tthis.data.label = labelValueDescriptionValidator.parse(label);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the value for this option.\n\t *\n\t * @param value - The value to use\n\t */\n\tpublic setValue(value: string) {\n\t\tthis.data.value = labelValueDescriptionValidator.parse(value);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the description for this option.\n\t *\n\t * @param description - The description to use\n\t */\n\tpublic setDescription(description: string) {\n\t\tthis.data.description = labelValueDescriptionValidator.parse(description);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether this option is selected by default.\n\t *\n\t * @param isDefault - Whether this option is selected by default\n\t */\n\tpublic setDefault(isDefault = true) {\n\t\tthis.data.default = defaultValidator.parse(isDefault);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the emoji to display for this option.\n\t *\n\t * @param emoji - The emoji to use\n\t */\n\tpublic setEmoji(emoji: APIMessageComponentEmoji) {\n\t\tthis.data.emoji = emojiValidator.parse(emoji);\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc BaseSelectMenuBuilder.toJSON}\n\t */\n\tpublic toJSON(): APISelectMenuOption {\n\t\tvalidateRequiredSelectMenuOptionParameters(this.data.label, this.data.value);\n\n\t\treturn {\n\t\t\t...this.data,\n\t\t} as APISelectMenuOption;\n\t}\n}\n", "/* eslint-disable jsdoc/check-param-names */\n\nimport {\n\ttype APIActionRowComponent,\n\tComponentType,\n\ttype APIComponentInMessageActionRow,\n\ttype APIComponentInModalActionRow,\n\ttype APIComponentInActionRow,\n} from 'discord-api-types/v10';\nimport { normalizeArray, type RestOrArray } from '../util/normalizeArray.js';\nimport { ComponentBuilder } from './Component.js';\nimport { createComponentBuilder } from './Components.js';\nimport type { ButtonBuilder } from './button/Button.js';\nimport type { ChannelSelectMenuBuilder } from './selectMenu/ChannelSelectMenu.js';\nimport type { MentionableSelectMenuBuilder } from './selectMenu/MentionableSelectMenu.js';\nimport type { RoleSelectMenuBuilder } from './selectMenu/RoleSelectMenu.js';\nimport type { StringSelectMenuBuilder } from './selectMenu/StringSelectMenu.js';\nimport type { UserSelectMenuBuilder } from './selectMenu/UserSelectMenu.js';\nimport type { TextInputBuilder } from './textInput/TextInput.js';\n\n/**\n * The builders that may be used for modals.\n */\nexport type ModalComponentBuilder = ActionRowBuilder<ModalActionRowComponentBuilder> | ModalActionRowComponentBuilder;\n\n/**\n * The builders that may be used within an action row for messages.\n */\nexport type MessageActionRowComponentBuilder =\n\t| ButtonBuilder\n\t| ChannelSelectMenuBuilder\n\t| MentionableSelectMenuBuilder\n\t| RoleSelectMenuBuilder\n\t| StringSelectMenuBuilder\n\t| UserSelectMenuBuilder;\n\n/**\n * The builders that may be used within an action row for modals.\n */\nexport type ModalActionRowComponentBuilder = TextInputBuilder;\n\n/**\n * Any builder.\n */\nexport type AnyComponentBuilder = MessageActionRowComponentBuilder | ModalActionRowComponentBuilder;\n\n/**\n * A builder that creates API-compatible JSON data for action rows.\n *\n * @typeParam ComponentType - The types of components this action row holds\n */\nexport class ActionRowBuilder<ComponentType extends AnyComponentBuilder> extends ComponentBuilder<\n\tAPIActionRowComponent<APIComponentInMessageActionRow | APIComponentInModalActionRow>\n> {\n\t/**\n\t * The components within this action row.\n\t */\n\tpublic readonly components: ComponentType[];\n\n\t/**\n\t * Creates a new action row from API data.\n\t *\n\t * @param data - The API data to create this action row with\n\t * @example\n\t * Creating an action row from an API data object:\n\t * ```ts\n\t * const actionRow = new ActionRowBuilder({\n\t * \tcomponents: [\n\t * \t\t{\n\t * \t\t\tcustom_id: \"custom id\",\n\t * \t\t\tlabel: \"Type something\",\n\t * \t\t\tstyle: TextInputStyle.Short,\n\t * \t\t\ttype: ComponentType.TextInput,\n\t * \t\t},\n\t * \t],\n\t * });\n\t * ```\n\t * @example\n\t * Creating an action row using setters and API data:\n\t * ```ts\n\t * const actionRow = new ActionRowBuilder({\n\t * \tcomponents: [\n\t * \t\t{\n\t * \t\t\tcustom_id: \"custom id\",\n\t * \t\t\tlabel: \"Click me\",\n\t * \t\t\tstyle: ButtonStyle.Primary,\n\t * \t\t\ttype: ComponentType.Button,\n\t * \t\t},\n\t * \t],\n\t * })\n\t * \t.addComponents(button2, button3);\n\t * ```\n\t */\n\tpublic constructor({ components, ...data }: Partial<APIActionRowComponent<APIComponentInActionRow>> = {}) {\n\t\tsuper({ type: ComponentType.ActionRow, ...data });\n\t\tthis.components = (components?.map((component) => createComponentBuilder(component)) ?? []) as ComponentType[];\n\t}\n\n\t/**\n\t * Adds components to this action row.\n\t *\n\t * @param components - The components to add\n\t */\n\tpublic addComponents(...components: RestOrArray<ComponentType>) {\n\t\tthis.components.push(...normalizeArray(components));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets components for this action row.\n\t *\n\t * @param components - The components to set\n\t */\n\tpublic setComponents(...components: RestOrArray<ComponentType>) {\n\t\tthis.components.splice(0, this.components.length, ...normalizeArray(components));\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic toJSON(): APIActionRowComponent<ReturnType<ComponentType['toJSON']>> {\n\t\treturn {\n\t\t\t...this.data,\n\t\t\tcomponents: this.components.map((component) => component.toJSON()),\n\t\t} as APIActionRowComponent<ReturnType<ComponentType['toJSON']>>;\n\t}\n}\n", "import type { J<PERSON>NEncodable } from '@discordjs/util';\nimport type {\n\tAPIActionRowComponent,\n\tAPIComponentInActionRow,\n\tAPIBaseComponent,\n\tComponentType,\n\tAPIMessageComponent,\n} from 'discord-api-types/v10';\nimport { idValidator } from './Assertions';\n\n/**\n * Any action row component data represented as an object.\n */\nexport type AnyAPIActionRowComponent =\n\t| APIActionRowComponent<APIComponentInActionRow>\n\t| APIComponentInActionRow\n\t| APIMessageComponent;\n\n/**\n * The base component builder that contains common symbols for all sorts of components.\n *\n * @typeParam DataType - The type of internal API data that is stored within the component\n */\nexport abstract class ComponentBuilder<\n\tDataType extends Partial<APIBaseComponent<ComponentType>> = APIBaseComponent<ComponentType>,\n> implements JSONEncodable<AnyAPIActionRowComponent>\n{\n\t/**\n\t * The API data associated with this component.\n\t */\n\tpublic readonly data: Partial<DataType>;\n\n\t/**\n\t * Serializes this builder to API-compatible JSON data.\n\t *\n\t * @remarks\n\t * This method runs validations on the data before serializing it.\n\t * As such, it may throw an error if the data is invalid.\n\t */\n\tpublic abstract toJSON(): AnyAPIActionRowComponent;\n\n\t/**\n\t * Constructs a new kind of component.\n\t *\n\t * @param data - The data to construct a component out of\n\t */\n\tpublic constructor(data: Partial<DataType>) {\n\t\tthis.data = data;\n\t}\n\n\t/**\n\t * Sets the id (not the custom id) for this component.\n\t *\n\t * @param id - The id for this component\n\t */\n\tpublic setId(id: number) {\n\t\tthis.data.id = idValidator.parse(id);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Clears the id of this component, defaulting to a default incremented id.\n\t */\n\tpublic clearId() {\n\t\tthis.data.id = undefined;\n\t\treturn this;\n\t}\n}\n", "import type { JSONEncodable } from '@discordjs/util';\nimport { ComponentType, type APIMessageComponent, type APIModalComponent } from 'discord-api-types/v10';\nimport {\n\tActionRowBuilder,\n\ttype MessageActionRowComponentBuilder,\n\ttype AnyComponentBuilder,\n\ttype ModalComponentBuilder,\n} from './ActionRow.js';\nimport { ComponentBuilder } from './Component.js';\nimport { ButtonBuilder } from './button/Button.js';\nimport { ChannelSelectMenuBuilder } from './selectMenu/ChannelSelectMenu.js';\nimport { MentionableSelectMenuBuilder } from './selectMenu/MentionableSelectMenu.js';\nimport { RoleSelectMenuBuilder } from './selectMenu/RoleSelectMenu.js';\nimport { StringSelectMenuBuilder } from './selectMenu/StringSelectMenu.js';\nimport { UserSelectMenuBuilder } from './selectMenu/UserSelectMenu.js';\nimport { TextInputBuilder } from './textInput/TextInput.js';\nimport { ContainerBuilder } from './v2/Container.js';\nimport { FileBuilder } from './v2/File.js';\nimport { MediaGalleryBuilder } from './v2/MediaGallery.js';\nimport { SectionBuilder } from './v2/Section.js';\nimport { SeparatorBuilder } from './v2/Separator.js';\nimport { TextDisplayBuilder } from './v2/TextDisplay.js';\nimport { ThumbnailBuilder } from './v2/Thumbnail.js';\n\n/**\n * The builders that may be used for messages.\n */\nexport type MessageComponentBuilder =\n\t| ActionRowBuilder<MessageActionRowComponentBuilder>\n\t| ContainerBuilder\n\t| FileBuilder\n\t| MediaGalleryBuilder\n\t| MessageActionRowComponentBuilder\n\t| SectionBuilder\n\t| SeparatorBuilder\n\t| TextDisplayBuilder\n\t| ThumbnailBuilder;\n\n/**\n * Components here are mapped to their respective builder.\n */\nexport interface MappedComponentTypes {\n\t/**\n\t * The action row component type is associated with an {@link ActionRowBuilder}.\n\t */\n\t[ComponentType.ActionRow]: ActionRowBuilder<AnyComponentBuilder>;\n\t/**\n\t * The button component type is associated with a {@link ButtonBuilder}.\n\t */\n\t[ComponentType.Button]: ButtonBuilder;\n\t/**\n\t * The string select component type is associated with a {@link StringSelectMenuBuilder}.\n\t */\n\t[ComponentType.StringSelect]: StringSelectMenuBuilder;\n\t/**\n\t * The text input component type is associated with a {@link TextInputBuilder}.\n\t */\n\t[ComponentType.TextInput]: TextInputBuilder;\n\t/**\n\t * The user select component type is associated with a {@link UserSelectMenuBuilder}.\n\t */\n\t[ComponentType.UserSelect]: UserSelectMenuBuilder;\n\t/**\n\t * The role select component type is associated with a {@link RoleSelectMenuBuilder}.\n\t */\n\t[ComponentType.RoleSelect]: RoleSelectMenuBuilder;\n\t/**\n\t * The mentionable select component type is associated with a {@link MentionableSelectMenuBuilder}.\n\t */\n\t[ComponentType.MentionableSelect]: MentionableSelectMenuBuilder;\n\t/**\n\t * The channel select component type is associated with a {@link ChannelSelectMenuBuilder}.\n\t */\n\t[ComponentType.ChannelSelect]: ChannelSelectMenuBuilder;\n\t/**\n\t * The file component type is associated with a {@link FileBuilder}.\n\t */\n\t[ComponentType.File]: FileBuilder;\n\t/**\n\t * The separator component type is associated with a {@link SeparatorBuilder}.\n\t */\n\t[ComponentType.Separator]: SeparatorBuilder;\n\t/**\n\t * The container component type is associated with a {@link ContainerBuilder}.\n\t */\n\t[ComponentType.Container]: ContainerBuilder;\n\t/**\n\t * The text display component type is associated with a {@link TextDisplayBuilder}.\n\t */\n\t[ComponentType.TextDisplay]: TextDisplayBuilder;\n\t/**\n\t * The thumbnail component type is associated with a {@link ThumbnailBuilder}.\n\t */\n\t[ComponentType.Thumbnail]: ThumbnailBuilder;\n\t/**\n\t * The section component type is associated with a {@link SectionBuilder}.\n\t */\n\t[ComponentType.Section]: SectionBuilder;\n\t/**\n\t * The media gallery component type is associated with a {@link MediaGalleryBuilder}.\n\t */\n\t[ComponentType.MediaGallery]: MediaGalleryBuilder;\n}\n\n/**\n * Factory for creating components from API data.\n *\n * @typeParam ComponentType - The type of component to use\n * @param data - The API data to transform to a component class\n */\nexport function createComponentBuilder<ComponentType extends keyof MappedComponentTypes>(\n\t// eslint-disable-next-line @typescript-eslint/sort-type-constituents\n\tdata: (APIModalComponent | APIMessageComponent) & { type: ComponentType },\n): MappedComponentTypes[ComponentType];\n\n/**\n * Factory for creating components from API data.\n *\n * @typeParam ComponentBuilder - The type of component to use\n * @param data - The API data to transform to a component class\n */\nexport function createComponentBuilder<ComponentBuilder extends MessageComponentBuilder | ModalComponentBuilder>(\n\tdata: ComponentBuilder,\n): ComponentBuilder;\n\nexport function createComponentBuilder(\n\tdata: APIMessageComponent | APIModalComponent | MessageComponentBuilder,\n): ComponentBuilder {\n\tif (data instanceof ComponentBuilder) {\n\t\treturn data;\n\t}\n\n\tswitch (data.type) {\n\t\tcase ComponentType.ActionRow:\n\t\t\treturn new ActionRowBuilder(data);\n\t\tcase ComponentType.Button:\n\t\t\treturn new ButtonBuilder(data);\n\t\tcase ComponentType.StringSelect:\n\t\t\treturn new StringSelectMenuBuilder(data);\n\t\tcase ComponentType.TextInput:\n\t\t\treturn new TextInputBuilder(data);\n\t\tcase ComponentType.UserSelect:\n\t\t\treturn new UserSelectMenuBuilder(data);\n\t\tcase ComponentType.RoleSelect:\n\t\t\treturn new RoleSelectMenuBuilder(data);\n\t\tcase ComponentType.MentionableSelect:\n\t\t\treturn new MentionableSelectMenuBuilder(data);\n\t\tcase ComponentType.ChannelSelect:\n\t\t\treturn new ChannelSelectMenuBuilder(data);\n\t\tcase ComponentType.File:\n\t\t\treturn new FileBuilder(data);\n\t\tcase ComponentType.Container:\n\t\t\treturn new ContainerBuilder(data);\n\t\tcase ComponentType.Section:\n\t\t\treturn new SectionBuilder(data);\n\t\tcase ComponentType.Separator:\n\t\t\treturn new SeparatorBuilder(data);\n\t\tcase ComponentType.TextDisplay:\n\t\t\treturn new TextDisplayBuilder(data);\n\t\tcase ComponentType.Thumbnail:\n\t\t\treturn new ThumbnailBuilder(data);\n\t\tcase ComponentType.MediaGallery:\n\t\t\treturn new MediaGalleryBuilder(data);\n\t\tdefault:\n\t\t\t// @ts-expect-error This case can still occur if we get a newer unsupported component type\n\t\t\tthrow new Error(`Cannot properly serialize component type: ${data.type}`);\n\t}\n}\n\nfunction isBuilder<Builder extends JSONEncodable<any>>(\n\tbuilder: unknown,\n\tConstructor: new () => Builder,\n): builder is Builder {\n\treturn builder instanceof Constructor;\n}\n\nexport function resolveBuilder<ComponentType extends Record<PropertyKey, any>, Builder extends JSONEncodable<any>>(\n\tbuilder: Builder | ComponentType | ((builder: Builder) => Builder),\n\tConstructor: new (data?: ComponentType) => Builder,\n) {\n\tif (isBuilder(builder, Constructor)) {\n\t\treturn builder;\n\t}\n\n\tif (typeof builder === 'function') {\n\t\treturn builder(new Constructor());\n\t}\n\n\treturn new Constructor(builder);\n}\n", "import {\n\tComponentType,\n\ttype APIButtonComponent,\n\ttype APIButtonComponentWithCustomId,\n\ttype APIButtonComponentWithSKUId,\n\ttype APIButtonComponentWithURL,\n\ttype APIMessageComponentEmoji,\n\ttype ButtonStyle,\n\ttype Snowflake,\n} from 'discord-api-types/v10';\nimport {\n\tbuttonLabelValidator,\n\tbuttonStyleValidator,\n\tcustomIdValidator,\n\tdisabledValidator,\n\temojiValidator,\n\turlValidator,\n\tvalidateRequiredButtonParameters,\n} from '../Assertions.js';\nimport { ComponentBuilder } from '../Component.js';\n\n/**\n * A builder that creates API-compatible JSON data for buttons.\n */\nexport class ButtonBuilder extends ComponentBuilder<APIButtonComponent> {\n\t/**\n\t * Creates a new button from API data.\n\t *\n\t * @param data - The API data to create this button with\n\t * @example\n\t * Creating a button from an API data object:\n\t * ```ts\n\t * const button = new ButtonBuilder({\n\t * \tcustom_id: 'a cool button',\n\t * \tstyle: ButtonStyle.Primary,\n\t * \tlabel: 'Click Me',\n\t * \temoji: {\n\t * \t\tname: 'smile',\n\t * \t\tid: '123456789012345678',\n\t * \t},\n\t * });\n\t * ```\n\t * @example\n\t * Creating a button using setters and API data:\n\t * ```ts\n\t * const button = new ButtonBuilder({\n\t * \tstyle: ButtonStyle.Secondary,\n\t * \tlabel: 'Click Me',\n\t * })\n\t * \t.setEmoji({ name: '🙂' })\n\t * \t.setCustomId('another cool button');\n\t * ```\n\t */\n\tpublic constructor(data?: Partial<APIButtonComponent>) {\n\t\tsuper({ type: ComponentType.Button, ...data });\n\t}\n\n\t/**\n\t * Sets the style of this button.\n\t *\n\t * @param style - The style to use\n\t */\n\tpublic setStyle(style: ButtonStyle) {\n\t\tthis.data.style = buttonStyleValidator.parse(style);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the URL for this button.\n\t *\n\t * @remarks\n\t * This method is only available to buttons using the `Link` button style.\n\t * Only three types of URL schemes are currently supported: `https://`, `http://`, and `discord://`.\n\t * @param url - The URL to use\n\t */\n\tpublic setURL(url: string) {\n\t\t(this.data as APIButtonComponentWithURL).url = urlValidator.parse(url);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the custom id for this button.\n\t *\n\t * @remarks\n\t * This method is only applicable to buttons that are not using the `Link` button style.\n\t * @param customId - The custom id to use\n\t */\n\tpublic setCustomId(customId: string) {\n\t\t(this.data as APIButtonComponentWithCustomId).custom_id = customIdValidator.parse(customId);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the SKU id that represents a purchasable SKU for this button.\n\t *\n\t * @remarks Only available when using premium-style buttons.\n\t * @param skuId - The SKU id to use\n\t */\n\tpublic setSKUId(skuId: Snowflake) {\n\t\t(this.data as APIButtonComponentWithSKUId).sku_id = skuId;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the emoji to display on this button.\n\t *\n\t * @param emoji - The emoji to use\n\t */\n\tpublic setEmoji(emoji: APIMessageComponentEmoji) {\n\t\t(this.data as Exclude<APIButtonComponent, APIButtonComponentWithSKUId>).emoji = emojiValidator.parse(emoji);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether this button is disabled.\n\t *\n\t * @param disabled - Whether to disable this button\n\t */\n\tpublic setDisabled(disabled = true) {\n\t\tthis.data.disabled = disabledValidator.parse(disabled);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the label for this button.\n\t *\n\t * @param label - The label to use\n\t */\n\tpublic setLabel(label: string) {\n\t\t(this.data as Exclude<APIButtonComponent, APIButtonComponentWithSKUId>).label = buttonLabelValidator.parse(label);\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic toJSON(): APIButtonComponent {\n\t\tvalidateRequiredButtonParameters(\n\t\t\tthis.data.style,\n\t\t\t(this.data as Exclude<APIButtonComponent, APIButtonComponentWithSKUId>).label,\n\t\t\t(this.data as Exclude<APIButtonComponent, APIButtonComponentWithSKUId>).emoji,\n\t\t\t(this.data as APIButtonComponentWithCustomId).custom_id,\n\t\t\t(this.data as APIButtonComponentWithSKUId).sku_id,\n\t\t\t(this.data as APIButtonComponentWithURL).url,\n\t\t);\n\n\t\treturn {\n\t\t\t...this.data,\n\t\t} as APIButtonComponent;\n\t}\n}\n", "import {\n\ttype APIChannelSelectComponent,\n\ttype ChannelType,\n\ttype Snowflake,\n\tComponentType,\n\tSelectMenuDefaultValueType,\n} from 'discord-api-types/v10';\nimport { type RestOrArray, normalizeArray } from '../../util/normalizeArray.js';\nimport { channelTypesValidator, customIdValidator, optionsLengthValidator } from '../Assertions.js';\nimport { BaseSelectMenuBuilder } from './BaseSelectMenu.js';\n\n/**\n * A builder that creates API-compatible JSON data for channel select menus.\n */\nexport class ChannelSelectMenuBuilder extends BaseSelectMenuBuilder<APIChannelSelectComponent> {\n\t/**\n\t * Creates a new select menu from API data.\n\t *\n\t * @param data - The API data to create this select menu with\n\t * @example\n\t * Creating a select menu from an API data object:\n\t * ```ts\n\t * const selectMenu = new ChannelSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * \tplaceholder: 'select an option',\n\t * \tmax_values: 2,\n\t * });\n\t * ```\n\t * @example\n\t * Creating a select menu using setters and API data:\n\t * ```ts\n\t * const selectMenu = new ChannelSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * })\n\t * \t.addChannelTypes(ChannelType.GuildText, ChannelType.GuildAnnouncement)\n\t * \t.setMinValues(2);\n\t * ```\n\t */\n\tpublic constructor(data?: Partial<APIChannelSelectComponent>) {\n\t\tsuper({ ...data, type: ComponentType.ChannelSelect });\n\t}\n\n\t/**\n\t * Adds channel types to this select menu.\n\t *\n\t * @param types - The channel types to use\n\t */\n\tpublic addChannelTypes(...types: RestOrArray<ChannelType>) {\n\t\tconst normalizedTypes = normalizeArray(types);\n\t\tthis.data.channel_types ??= [];\n\t\tthis.data.channel_types.push(...channelTypesValidator.parse(normalizedTypes));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets channel types for this select menu.\n\t *\n\t * @param types - The channel types to use\n\t */\n\tpublic setChannelTypes(...types: RestOrArray<ChannelType>) {\n\t\tconst normalizedTypes = normalizeArray(types);\n\t\tthis.data.channel_types ??= [];\n\t\tthis.data.channel_types.splice(0, this.data.channel_types.length, ...channelTypesValidator.parse(normalizedTypes));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds default channels to this auto populated select menu.\n\t *\n\t * @param channels - The channels to add\n\t */\n\tpublic addDefaultChannels(...channels: RestOrArray<Snowflake>) {\n\t\tconst normalizedValues = normalizeArray(channels);\n\t\toptionsLengthValidator.parse((this.data.default_values?.length ?? 0) + normalizedValues.length);\n\t\tthis.data.default_values ??= [];\n\n\t\tthis.data.default_values.push(\n\t\t\t...normalizedValues.map((id) => ({\n\t\t\t\tid,\n\t\t\t\ttype: SelectMenuDefaultValueType.Channel as const,\n\t\t\t})),\n\t\t);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets default channels for this auto populated select menu.\n\t *\n\t * @param channels - The channels to set\n\t */\n\tpublic setDefaultChannels(...channels: RestOrArray<Snowflake>) {\n\t\tconst normalizedValues = normalizeArray(channels);\n\t\toptionsLengthValidator.parse(normalizedValues.length);\n\n\t\tthis.data.default_values = normalizedValues.map((id) => ({\n\t\t\tid,\n\t\t\ttype: SelectMenuDefaultValueType.Channel as const,\n\t\t}));\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc BaseSelectMenuBuilder.toJSON}\n\t */\n\tpublic override toJSON(): APIChannelSelectComponent {\n\t\tcustomIdValidator.parse(this.data.custom_id);\n\n\t\treturn {\n\t\t\t...this.data,\n\t\t} as APIChannelSelectComponent;\n\t}\n}\n", "import type { APISelectMenuComponent } from 'discord-api-types/v10';\nimport { customIdValidator, disabledValidator, minMaxValidator, placeholderValidator } from '../Assertions.js';\nimport { ComponentBuilder } from '../Component.js';\n\n/**\n * The base select menu builder that contains common symbols for select menu builders.\n *\n * @typeParam SelectMenuType - The type of select menu this would be instantiated for.\n */\nexport abstract class BaseSelectMenuBuilder<\n\tSelectMenuType extends APISelectMenuComponent,\n> extends ComponentBuilder<SelectMenuType> {\n\t/**\n\t * Sets the placeholder for this select menu.\n\t *\n\t * @param placeholder - The placeholder to use\n\t */\n\tpublic setPlaceholder(placeholder: string) {\n\t\tthis.data.placeholder = placeholderValidator.parse(placeholder);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the minimum values that must be selected in the select menu.\n\t *\n\t * @param minValues - The minimum values that must be selected\n\t */\n\tpublic setMinValues(minValues: number) {\n\t\tthis.data.min_values = minMaxValidator.parse(minValues);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the maximum values that must be selected in the select menu.\n\t *\n\t * @param maxValues - The maximum values that must be selected\n\t */\n\tpublic setMaxValues(maxValues: number) {\n\t\tthis.data.max_values = minMaxValidator.parse(maxValues);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the custom id for this select menu.\n\t *\n\t * @param customId - The custom id to use\n\t */\n\tpublic setCustomId(customId: string) {\n\t\tthis.data.custom_id = customIdValidator.parse(customId);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether this select menu is disabled.\n\t *\n\t * @param disabled - Whether this select menu is disabled\n\t */\n\tpublic setDisabled(disabled = true) {\n\t\tthis.data.disabled = disabledValidator.parse(disabled);\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic toJSON(): SelectMenuType {\n\t\tcustomIdValidator.parse(this.data.custom_id);\n\t\treturn {\n\t\t\t...this.data,\n\t\t} as SelectMenuType;\n\t}\n}\n", "import {\n\ttype APIMentionableSelectComponent,\n\ttype APISelectMenuDefaultValue,\n\ttype Snowflake,\n\tComponentType,\n\tSelectMenuDefaultValueType,\n} from 'discord-api-types/v10';\nimport { type RestOrArray, normalizeArray } from '../../util/normalizeArray.js';\nimport { optionsLengthValidator } from '../Assertions.js';\nimport { BaseSelectMenuBuilder } from './BaseSelectMenu.js';\n\n/**\n * A builder that creates API-compatible JSON data for mentionable select menus.\n */\nexport class MentionableSelectMenuBuilder extends BaseSelectMenuBuilder<APIMentionableSelectComponent> {\n\t/**\n\t * Creates a new select menu from API data.\n\t *\n\t * @param data - The API data to create this select menu with\n\t * @example\n\t * Creating a select menu from an API data object:\n\t * ```ts\n\t * const selectMenu = new MentionableSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * \tplaceholder: 'select an option',\n\t * \tmax_values: 2,\n\t * });\n\t * ```\n\t * @example\n\t * Creating a select menu using setters and API data:\n\t * ```ts\n\t * const selectMenu = new MentionableSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * })\n\t * \t.setMinValues(1);\n\t * ```\n\t */\n\tpublic constructor(data?: Partial<APIMentionableSelectComponent>) {\n\t\tsuper({ ...data, type: ComponentType.MentionableSelect });\n\t}\n\n\t/**\n\t * Adds default roles to this auto populated select menu.\n\t *\n\t * @param roles - The roles to add\n\t */\n\tpublic addDefaultRoles(...roles: RestOrArray<Snowflake>) {\n\t\tconst normalizedValues = normalizeArray(roles);\n\t\toptionsLengthValidator.parse((this.data.default_values?.length ?? 0) + normalizedValues.length);\n\t\tthis.data.default_values ??= [];\n\n\t\tthis.data.default_values.push(\n\t\t\t...normalizedValues.map((id) => ({\n\t\t\t\tid,\n\t\t\t\ttype: SelectMenuDefaultValueType.Role as const,\n\t\t\t})),\n\t\t);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds default users to this auto populated select menu.\n\t *\n\t * @param users - The users to add\n\t */\n\tpublic addDefaultUsers(...users: RestOrArray<Snowflake>) {\n\t\tconst normalizedValues = normalizeArray(users);\n\t\toptionsLengthValidator.parse((this.data.default_values?.length ?? 0) + normalizedValues.length);\n\t\tthis.data.default_values ??= [];\n\n\t\tthis.data.default_values.push(\n\t\t\t...normalizedValues.map((id) => ({\n\t\t\t\tid,\n\t\t\t\ttype: SelectMenuDefaultValueType.User as const,\n\t\t\t})),\n\t\t);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds default values to this auto populated select menu.\n\t *\n\t * @param values - The values to add\n\t */\n\tpublic addDefaultValues(\n\t\t...values: RestOrArray<\n\t\t\t| APISelectMenuDefaultValue<SelectMenuDefaultValueType.Role>\n\t\t\t| APISelectMenuDefaultValue<SelectMenuDefaultValueType.User>\n\t\t>\n\t) {\n\t\tconst normalizedValues = normalizeArray(values);\n\t\toptionsLengthValidator.parse((this.data.default_values?.length ?? 0) + normalizedValues.length);\n\t\tthis.data.default_values ??= [];\n\t\tthis.data.default_values.push(...normalizedValues);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets default values for this auto populated select menu.\n\t *\n\t * @param values - The values to set\n\t */\n\tpublic setDefaultValues(\n\t\t...values: RestOrArray<\n\t\t\t| APISelectMenuDefaultValue<SelectMenuDefaultValueType.Role>\n\t\t\t| APISelectMenuDefaultValue<SelectMenuDefaultValueType.User>\n\t\t>\n\t) {\n\t\tconst normalizedValues = normalizeArray(values);\n\t\toptionsLengthValidator.parse(normalizedValues.length);\n\t\tthis.data.default_values = normalizedValues;\n\t\treturn this;\n\t}\n}\n", "import {\n\ttype APIRoleSelectComponent,\n\ttype Snowflake,\n\tComponentType,\n\tSelectMenuDefaultValueType,\n} from 'discord-api-types/v10';\nimport { type RestOrArray, normalizeArray } from '../../util/normalizeArray.js';\nimport { optionsLengthValidator } from '../Assertions.js';\nimport { BaseSelectMenuBuilder } from './BaseSelectMenu.js';\n\n/**\n * A builder that creates API-compatible JSON data for role select menus.\n */\nexport class RoleSelectMenuBuilder extends BaseSelectMenuBuilder<APIRoleSelectComponent> {\n\t/**\n\t * Creates a new select menu from API data.\n\t *\n\t * @param data - The API data to create this select menu with\n\t * @example\n\t * Creating a select menu from an API data object:\n\t * ```ts\n\t * const selectMenu = new RoleSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * \tplaceholder: 'select an option',\n\t * \tmax_values: 2,\n\t * });\n\t * ```\n\t * @example\n\t * Creating a select menu using setters and API data:\n\t * ```ts\n\t * const selectMenu = new RoleSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * })\n\t * \t.setMinValues(1);\n\t * ```\n\t */\n\tpublic constructor(data?: Partial<APIRoleSelectComponent>) {\n\t\tsuper({ ...data, type: ComponentType.RoleSelect });\n\t}\n\n\t/**\n\t * Adds default roles to this auto populated select menu.\n\t *\n\t * @param roles - The roles to add\n\t */\n\tpublic addDefaultRoles(...roles: RestOrArray<Snowflake>) {\n\t\tconst normalizedValues = normalizeArray(roles);\n\t\toptionsLengthValidator.parse((this.data.default_values?.length ?? 0) + normalizedValues.length);\n\t\tthis.data.default_values ??= [];\n\n\t\tthis.data.default_values.push(\n\t\t\t...normalizedValues.map((id) => ({\n\t\t\t\tid,\n\t\t\t\ttype: SelectMenuDefaultValueType.Role as const,\n\t\t\t})),\n\t\t);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets default roles for this auto populated select menu.\n\t *\n\t * @param roles - The roles to set\n\t */\n\tpublic setDefaultRoles(...roles: RestOrArray<Snowflake>) {\n\t\tconst normalizedValues = normalizeArray(roles);\n\t\toptionsLengthValidator.parse(normalizedValues.length);\n\n\t\tthis.data.default_values = normalizedValues.map((id) => ({\n\t\t\tid,\n\t\t\ttype: SelectMenuDefaultValueType.Role as const,\n\t\t}));\n\n\t\treturn this;\n\t}\n}\n", "import { ComponentType } from 'discord-api-types/v10';\nimport type { APIStringSelectComponent, APISelectMenuOption } from 'discord-api-types/v10';\nimport { normalizeArray, type RestOrArray } from '../../util/normalizeArray.js';\nimport { jsonOptionValidator, optionsLengthValidator, validateRequiredSelectMenuParameters } from '../Assertions.js';\nimport { BaseSelectMenuBuilder } from './BaseSelectMenu.js';\nimport { StringSelectMenuOptionBuilder } from './StringSelectMenuOption.js';\n\n/**\n * A builder that creates API-compatible JSON data for string select menus.\n */\nexport class StringSelectMenuBuilder extends BaseSelectMenuBuilder<APIStringSelectComponent> {\n\t/**\n\t * The options within this select menu.\n\t */\n\tpublic readonly options: StringSelectMenuOptionBuilder[];\n\n\t/**\n\t * Creates a new select menu from API data.\n\t *\n\t * @param data - The API data to create this select menu with\n\t * @example\n\t * Creating a select menu from an API data object:\n\t * ```ts\n\t * const selectMenu = new StringSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * \tplaceholder: 'select an option',\n\t * \tmax_values: 2,\n\t * \toptions: [\n\t * \t\t{ label: 'option 1', value: '1' },\n\t * \t\t{ label: 'option 2', value: '2' },\n\t * \t\t{ label: 'option 3', value: '3' },\n\t * \t],\n\t * });\n\t * ```\n\t * @example\n\t * Creating a select menu using setters and API data:\n\t * ```ts\n\t * const selectMenu = new StringSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * })\n\t * \t.setMinValues(1)\n\t * \t.addOptions({\n\t * \t\tlabel: 'Catchy',\n\t * \t\tvalue: 'catch',\n\t * \t});\n\t * ```\n\t */\n\tpublic constructor(data?: Partial<APIStringSelectComponent>) {\n\t\tconst { options, ...initData } = data ?? {};\n\t\tsuper({ ...initData, type: ComponentType.StringSelect });\n\t\tthis.options = options?.map((option: APISelectMenuOption) => new StringSelectMenuOptionBuilder(option)) ?? [];\n\t}\n\n\t/**\n\t * Adds options to this select menu.\n\t *\n\t * @param options - The options to add\n\t */\n\tpublic addOptions(...options: RestOrArray<APISelectMenuOption | StringSelectMenuOptionBuilder>) {\n\t\tconst normalizedOptions = normalizeArray(options);\n\t\toptionsLengthValidator.parse(this.options.length + normalizedOptions.length);\n\t\tthis.options.push(\n\t\t\t...normalizedOptions.map((normalizedOption) =>\n\t\t\t\tnormalizedOption instanceof StringSelectMenuOptionBuilder\n\t\t\t\t\t? normalizedOption\n\t\t\t\t\t: new StringSelectMenuOptionBuilder(jsonOptionValidator.parse(normalizedOption)),\n\t\t\t),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the options for this select menu.\n\t *\n\t * @param options - The options to set\n\t */\n\tpublic setOptions(...options: RestOrArray<APISelectMenuOption | StringSelectMenuOptionBuilder>) {\n\t\treturn this.spliceOptions(0, this.options.length, ...options);\n\t}\n\n\t/**\n\t * Removes, replaces, or inserts options for this select menu.\n\t *\n\t * @remarks\n\t * This method behaves similarly\n\t * to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/slice | Array.prototype.splice()}.\n\t * It's useful for modifying and adjusting the order of existing options.\n\t * @example\n\t * Remove the first option:\n\t * ```ts\n\t * selectMenu.spliceOptions(0, 1);\n\t * ```\n\t * @example\n\t * Remove the first n option:\n\t * ```ts\n\t * const n = 4;\n\t * selectMenu.spliceOptions(0, n);\n\t * ```\n\t * @example\n\t * Remove the last option:\n\t * ```ts\n\t * selectMenu.spliceOptions(-1, 1);\n\t * ```\n\t * @param index - The index to start at\n\t * @param deleteCount - The number of options to remove\n\t * @param options - The replacing option objects or builders\n\t */\n\tpublic spliceOptions(\n\t\tindex: number,\n\t\tdeleteCount: number,\n\t\t...options: RestOrArray<APISelectMenuOption | StringSelectMenuOptionBuilder>\n\t) {\n\t\tconst normalizedOptions = normalizeArray(options);\n\n\t\tconst clone = [...this.options];\n\n\t\tclone.splice(\n\t\t\tindex,\n\t\t\tdeleteCount,\n\t\t\t...normalizedOptions.map((normalizedOption) =>\n\t\t\t\tnormalizedOption instanceof StringSelectMenuOptionBuilder\n\t\t\t\t\t? normalizedOption\n\t\t\t\t\t: new StringSelectMenuOptionBuilder(jsonOptionValidator.parse(normalizedOption)),\n\t\t\t),\n\t\t);\n\n\t\toptionsLengthValidator.parse(clone.length);\n\t\tthis.options.splice(0, this.options.length, ...clone);\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc BaseSelectMenuBuilder.toJSON}\n\t */\n\tpublic override toJSON(): APIStringSelectComponent {\n\t\tvalidateRequiredSelectMenuParameters(this.options, this.data.custom_id);\n\n\t\treturn {\n\t\t\t...this.data,\n\t\t\toptions: this.options.map((option) => option.toJSON()),\n\t\t} as APIStringSelectComponent;\n\t}\n}\n", "import {\n\ttype APIUserSelectComponent,\n\ttype Snowflake,\n\tComponentType,\n\tSelectMenuDefaultValueType,\n} from 'discord-api-types/v10';\nimport { type RestOrArray, normalizeArray } from '../../util/normalizeArray.js';\nimport { optionsLengthValidator } from '../Assertions.js';\nimport { BaseSelectMenuBuilder } from './BaseSelectMenu.js';\n\n/**\n * A builder that creates API-compatible JSON data for user select menus.\n */\nexport class UserSelectMenuBuilder extends BaseSelectMenuBuilder<APIUserSelectComponent> {\n\t/**\n\t * Creates a new select menu from API data.\n\t *\n\t * @param data - The API data to create this select menu with\n\t * @example\n\t * Creating a select menu from an API data object:\n\t * ```ts\n\t * const selectMenu = new UserSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * \tplaceholder: 'select an option',\n\t * \tmax_values: 2,\n\t * });\n\t * ```\n\t * @example\n\t * Creating a select menu using setters and API data:\n\t * ```ts\n\t * const selectMenu = new UserSelectMenuBuilder({\n\t * \tcustom_id: 'a cool select menu',\n\t * })\n\t * \t.setMinValues(1);\n\t * ```\n\t */\n\tpublic constructor(data?: Partial<APIUserSelectComponent>) {\n\t\tsuper({ ...data, type: ComponentType.UserSelect });\n\t}\n\n\t/**\n\t * Adds default users to this auto populated select menu.\n\t *\n\t * @param users - The users to add\n\t */\n\tpublic addDefaultUsers(...users: RestOrArray<Snowflake>) {\n\t\tconst normalizedValues = normalizeArray(users);\n\t\toptionsLengthValidator.parse((this.data.default_values?.length ?? 0) + normalizedValues.length);\n\t\tthis.data.default_values ??= [];\n\n\t\tthis.data.default_values.push(\n\t\t\t...normalizedValues.map((id) => ({\n\t\t\t\tid,\n\t\t\t\ttype: SelectMenuDefaultValueType.User as const,\n\t\t\t})),\n\t\t);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets default users for this auto populated select menu.\n\t *\n\t * @param users - The users to set\n\t */\n\tpublic setDefaultUsers(...users: RestOrArray<Snowflake>) {\n\t\tconst normalizedValues = normalizeArray(users);\n\t\toptionsLengthValidator.parse(normalizedValues.length);\n\n\t\tthis.data.default_values = normalizedValues.map((id) => ({\n\t\t\tid,\n\t\t\ttype: SelectMenuDefaultValueType.User as const,\n\t\t}));\n\n\t\treturn this;\n\t}\n}\n", "import { isJSONEncodable, type Equatable, type JSONEncodable } from '@discordjs/util';\nimport { ComponentType, type TextInputStyle, type APITextInputComponent } from 'discord-api-types/v10';\nimport isEqual from 'fast-deep-equal';\nimport { customIdValidator } from '../Assertions.js';\nimport { ComponentBuilder } from '../Component.js';\nimport {\n\tmaxLengthValidator,\n\tminLengthValidator,\n\tplaceholderValidator,\n\trequiredValidator,\n\tvalueValidator,\n\tvalidateRequiredParameters,\n\tlabelValidator,\n\ttextInputStyleValidator,\n} from './Assertions.js';\n\n/**\n * A builder that creates API-compatible JSON data for text inputs.\n */\nexport class TextInputBuilder\n\textends ComponentBuilder<APITextInputComponent>\n\timplements Equatable<APITextInputComponent | JSONEncodable<APITextInputComponent>>\n{\n\t/**\n\t * Creates a new text input from API data.\n\t *\n\t * @param data - The API data to create this text input with\n\t * @example\n\t * Creating a text input from an API data object:\n\t * ```ts\n\t * const textInput = new TextInputBuilder({\n\t * \tcustom_id: 'a cool text input',\n\t * \tlabel: 'Type something',\n\t * \tstyle: TextInputStyle.Short,\n\t * });\n\t * ```\n\t * @example\n\t * Creating a text input using setters and API data:\n\t * ```ts\n\t * const textInput = new TextInputBuilder({\n\t * \tlabel: 'Type something else',\n\t * })\n\t * \t.setCustomId('woah')\n\t * \t.setStyle(TextInputStyle.Paragraph);\n\t * ```\n\t */\n\tpublic constructor(data?: APITextInputComponent & { type?: ComponentType.TextInput }) {\n\t\tsuper({ type: ComponentType.TextInput, ...data });\n\t}\n\n\t/**\n\t * Sets the custom id for this text input.\n\t *\n\t * @param customId - The custom id to use\n\t */\n\tpublic setCustomId(customId: string) {\n\t\tthis.data.custom_id = customIdValidator.parse(customId);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the label for this text input.\n\t *\n\t * @param label - The label to use\n\t */\n\tpublic setLabel(label: string) {\n\t\tthis.data.label = labelValidator.parse(label);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the style for this text input.\n\t *\n\t * @param style - The style to use\n\t */\n\tpublic setStyle(style: TextInputStyle) {\n\t\tthis.data.style = textInputStyleValidator.parse(style);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the minimum length of text for this text input.\n\t *\n\t * @param minLength - The minimum length of text for this text input\n\t */\n\tpublic setMinLength(minLength: number) {\n\t\tthis.data.min_length = minLengthValidator.parse(minLength);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the maximum length of text for this text input.\n\t *\n\t * @param maxLength - The maximum length of text for this text input\n\t */\n\tpublic setMaxLength(maxLength: number) {\n\t\tthis.data.max_length = maxLengthValidator.parse(maxLength);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the placeholder for this text input.\n\t *\n\t * @param placeholder - The placeholder to use\n\t */\n\tpublic setPlaceholder(placeholder: string) {\n\t\tthis.data.placeholder = placeholderValidator.parse(placeholder);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the value for this text input.\n\t *\n\t * @param value - The value to use\n\t */\n\tpublic setValue(value: string) {\n\t\tthis.data.value = valueValidator.parse(value);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether this text input is required.\n\t *\n\t * @param required - Whether this text input is required\n\t */\n\tpublic setRequired(required = true) {\n\t\tthis.data.required = requiredValidator.parse(required);\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic toJSON(): APITextInputComponent {\n\t\tvalidateRequiredParameters(this.data.custom_id, this.data.style, this.data.label);\n\n\t\treturn {\n\t\t\t...this.data,\n\t\t} as APITextInputComponent;\n\t}\n\n\t/**\n\t * Whether this is equal to another structure.\n\t */\n\tpublic equals(other: APITextInputComponent | JSONEncodable<APITextInputComponent>): boolean {\n\t\tif (isJSONEncodable(other)) {\n\t\t\treturn isEqual(other.toJSON(), this.data);\n\t\t}\n\n\t\treturn isEqual(other, this.data);\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { TextInputStyle } from 'discord-api-types/v10';\nimport { isValidationEnabled } from '../../util/validation.js';\nimport { customIdValidator } from '../Assertions.js';\n\nexport const textInputStyleValidator = s.nativeEnum(TextInputStyle);\nexport const minLengthValidator = s\n\t.number()\n\t.int()\n\t.greaterThanOrEqual(0)\n\t.lessThanOrEqual(4_000)\n\t.setValidationEnabled(isValidationEnabled);\nexport const maxLengthValidator = s\n\t.number()\n\t.int()\n\t.greaterThanOrEqual(1)\n\t.lessThanOrEqual(4_000)\n\t.setValidationEnabled(isValidationEnabled);\nexport const requiredValidator = s.boolean();\nexport const valueValidator = s.string().lengthLessThanOrEqual(4_000).setValidationEnabled(isValidationEnabled);\nexport const placeholderValidator = s.string().lengthLessThanOrEqual(100).setValidationEnabled(isValidationEnabled);\nexport const labelValidator = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(45)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateRequiredParameters(customId?: string, style?: TextInputStyle, label?: string) {\n\tcustomIdValidator.parse(customId);\n\ttextInputStyleValidator.parse(style);\n\tlabelValidator.parse(label);\n}\n", "/* eslint-disable jsdoc/check-param-names */\n\nimport type {\n\tAPIActionRowComponent,\n\tAPIComponentInContainer,\n\tAPIComponentInMessageActionRow,\n\tAPIContainerComponent,\n\tAPIFileComponent,\n\tAPIMediaGalleryComponent,\n\tAPISectionComponent,\n\tAPISeparatorComponent,\n\tAPITextDisplayComponent,\n} from 'discord-api-types/v10';\nimport { ComponentType } from 'discord-api-types/v10';\nimport type { RGBTuple } from '../../index.js';\nimport { MediaGalleryBuilder, SectionBuilder } from '../../index.js';\nimport { normalizeArray, type RestOrArray } from '../../util/normalizeArray.js';\nimport type { AnyComponentBuilder, MessageActionRowComponentBuilder } from '../ActionRow.js';\nimport { ActionRowBuilder } from '../ActionRow.js';\nimport { ComponentBuilder } from '../Component.js';\nimport { createComponentBuilder, resolveBuilder } from '../Components.js';\nimport { containerColorPredicate, spoilerPredicate } from './Assertions.js';\nimport { FileBuilder } from './File.js';\nimport { SeparatorBuilder } from './Separator.js';\nimport { TextDisplayBuilder } from './TextDisplay.js';\n\n/**\n * The builders that may be used within a container.\n */\nexport type ContainerComponentBuilder =\n\t| ActionRowBuilder<AnyComponentBuilder>\n\t| FileBuilder\n\t| MediaGalleryBuilder\n\t| SectionBuilder\n\t| SeparatorBuilder\n\t| TextDisplayBuilder;\n\n/**\n * A builder that creates API-compatible JSON data for a container.\n */\nexport class ContainerBuilder extends ComponentBuilder<APIContainerComponent> {\n\t/**\n\t * The components within this container.\n\t */\n\tpublic readonly components: ContainerComponentBuilder[];\n\n\t/**\n\t * Creates a new container from API data.\n\t *\n\t * @param data - The API data to create this container with\n\t * @example\n\t * Creating a container from an API data object:\n\t * ```ts\n\t * const container = new ContainerBuilder({\n\t * \tcomponents: [\n\t * \t\t{\n\t * \t\t\tcontent: \"Some text here\",\n\t * \t\t\ttype: ComponentType.TextDisplay,\n\t * \t\t},\n\t * \t],\n\t * });\n\t * ```\n\t * @example\n\t * Creating a container using setters and API data:\n\t * ```ts\n\t * const container = new ContainerBuilder({\n\t * \tcomponents: [\n\t * \t\t{\n\t * \t\t\tcontent: \"# Heading\",\n\t * \t\t\ttype: ComponentType.TextDisplay,\n\t * \t\t},\n\t * \t],\n\t * })\n\t * \t.addComponents(separator, section);\n\t * ```\n\t */\n\tpublic constructor({ components, ...data }: Partial<APIContainerComponent> = {}) {\n\t\tsuper({ type: ComponentType.Container, ...data });\n\t\tthis.components = (components?.map((component) => createComponentBuilder(component)) ??\n\t\t\t[]) as ContainerComponentBuilder[];\n\t}\n\n\t/**\n\t * Sets the accent color of this container.\n\t *\n\t * @param color - The color to use\n\t */\n\tpublic setAccentColor(color?: RGBTuple | number): this {\n\t\t// Data assertions\n\t\tcontainerColorPredicate.parse(color);\n\n\t\tif (Array.isArray(color)) {\n\t\t\tconst [red, green, blue] = color;\n\t\t\tthis.data.accent_color = (red << 16) + (green << 8) + blue;\n\t\t\treturn this;\n\t\t}\n\n\t\tthis.data.accent_color = color;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Clears the accent color of this container.\n\t */\n\tpublic clearAccentColor() {\n\t\tthis.data.accent_color = undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds action row components to this container.\n\t *\n\t * @param components - The action row components to add\n\t */\n\tpublic addActionRowComponents<ComponentType extends MessageActionRowComponentBuilder>(\n\t\t...components: RestOrArray<\n\t\t\t| ActionRowBuilder<ComponentType>\n\t\t\t| APIActionRowComponent<APIComponentInMessageActionRow>\n\t\t\t| ((builder: ActionRowBuilder<ComponentType>) => ActionRowBuilder<ComponentType>)\n\t\t>\n\t) {\n\t\tthis.components.push(\n\t\t\t...normalizeArray(components).map((component) => resolveBuilder(component, ActionRowBuilder<ComponentType>)),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds file components to this container.\n\t *\n\t * @param components - The file components to add\n\t */\n\tpublic addFileComponents(\n\t\t...components: RestOrArray<APIFileComponent | FileBuilder | ((builder: FileBuilder) => FileBuilder)>\n\t) {\n\t\tthis.components.push(...normalizeArray(components).map((component) => resolveBuilder(component, FileBuilder)));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds media gallery components to this container.\n\t *\n\t * @param components - The media gallery components to add\n\t */\n\tpublic addMediaGalleryComponents(\n\t\t...components: RestOrArray<\n\t\t\tAPIMediaGalleryComponent | MediaGalleryBuilder | ((builder: MediaGalleryBuilder) => MediaGalleryBuilder)\n\t\t>\n\t) {\n\t\tthis.components.push(\n\t\t\t...normalizeArray(components).map((component) => resolveBuilder(component, MediaGalleryBuilder)),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds section components to this container.\n\t *\n\t * @param components - The section components to add\n\t */\n\tpublic addSectionComponents(\n\t\t...components: RestOrArray<APISectionComponent | SectionBuilder | ((builder: SectionBuilder) => SectionBuilder)>\n\t) {\n\t\tthis.components.push(...normalizeArray(components).map((component) => resolveBuilder(component, SectionBuilder)));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds separator components to this container.\n\t *\n\t * @param components - The separator components to add\n\t */\n\tpublic addSeparatorComponents(\n\t\t...components: RestOrArray<\n\t\t\tAPISeparatorComponent | SeparatorBuilder | ((builder: SeparatorBuilder) => SeparatorBuilder)\n\t\t>\n\t) {\n\t\tthis.components.push(...normalizeArray(components).map((component) => resolveBuilder(component, SeparatorBuilder)));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds text display components to this container.\n\t *\n\t * @param components - The text display components to add\n\t */\n\tpublic addTextDisplayComponents(\n\t\t...components: RestOrArray<\n\t\t\tAPITextDisplayComponent | TextDisplayBuilder | ((builder: TextDisplayBuilder) => TextDisplayBuilder)\n\t\t>\n\t) {\n\t\tthis.components.push(\n\t\t\t...normalizeArray(components).map((component) => resolveBuilder(component, TextDisplayBuilder)),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Removes, replaces, or inserts components for this container.\n\t *\n\t * @param index - The index to start removing, replacing or inserting components\n\t * @param deleteCount - The amount of components to remove\n\t * @param components - The components to set\n\t */\n\tpublic spliceComponents(\n\t\tindex: number,\n\t\tdeleteCount: number,\n\t\t...components: RestOrArray<APIComponentInContainer | ContainerComponentBuilder>\n\t) {\n\t\tthis.components.splice(\n\t\t\tindex,\n\t\t\tdeleteCount,\n\t\t\t...normalizeArray(components).map((component) =>\n\t\t\t\tcomponent instanceof ComponentBuilder ? component : createComponentBuilder(component),\n\t\t\t),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the spoiler status of this container.\n\t *\n\t * @param spoiler - The spoiler status to use\n\t */\n\tpublic setSpoiler(spoiler = true) {\n\t\tthis.data.spoiler = spoilerPredicate.parse(spoiler);\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic toJSON(): APIContainerComponent {\n\t\treturn {\n\t\t\t...this.data,\n\t\t\tcomponents: this.components.map((component) => component.toJSON()),\n\t\t} as APIContainerComponent;\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { SeparatorSpacingSize } from 'discord-api-types/v10';\nimport { colorPredicate } from '../../messages/embed/Assertions';\nimport { isValidationEnabled } from '../../util/validation';\nimport { ComponentBuilder } from '../Component';\nimport { ButtonBuilder } from '../button/Button';\nimport type { ContainerComponentBuilder } from './Container';\nimport type { MediaGalleryItemBuilder } from './MediaGalleryItem';\nimport type { TextDisplayBuilder } from './TextDisplay';\nimport { ThumbnailBuilder } from './Thumbnail';\n\nexport const unfurledMediaItemPredicate = s\n\t.object({\n\t\turl: s\n\t\t\t.string()\n\t\t\t.url(\n\t\t\t\t{ allowedProtocols: ['http:', 'https:', 'attachment:'] },\n\t\t\t\t{ message: 'Invalid protocol for media URL. Must be http:, https:, or attachment:' },\n\t\t\t),\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const descriptionPredicate = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(1_024)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const filePredicate = s\n\t.object({\n\t\turl: s\n\t\t\t.string()\n\t\t\t.url({ allowedProtocols: ['attachment:'] }, { message: 'Invalid protocol for file URL. Must be attachment:' }),\n\t})\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const spoilerPredicate = s.boolean();\n\nexport const dividerPredicate = s.boolean();\n\nexport const spacingPredicate = s.nativeEnum(SeparatorSpacingSize);\n\nexport const textDisplayContentPredicate = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(4_000)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const accessoryPredicate = s\n\t.instance(ButtonBuilder)\n\t.or(s.instance(ThumbnailBuilder))\n\t.setValidationEnabled(isValidationEnabled);\n\nexport const containerColorPredicate = colorPredicate.nullish();\n\nexport function assertReturnOfBuilder<ReturnType extends MediaGalleryItemBuilder | TextDisplayBuilder>(\n\tinput: unknown,\n\tExpectedInstanceOf: new () => ReturnType,\n): asserts input is ReturnType {\n\ts.instance(ExpectedInstanceOf).setValidationEnabled(isValidationEnabled).parse(input);\n}\n\nexport function validateComponentArray<\n\tReturnType extends ContainerComponentBuilder | MediaGalleryItemBuilder = ContainerComponentBuilder,\n>(input: unknown, min: number, max: number, ExpectedInstanceOf?: new () => ReturnType): asserts input is ReturnType[] {\n\t(ExpectedInstanceOf ? s.instance(ExpectedInstanceOf) : s.instance(ComponentBuilder))\n\t\t.array()\n\t\t.lengthGreaterThanOrEqual(min)\n\t\t.lengthLessThanOrEqual(max)\n\t\t.setValidationEnabled(isValidationEnabled)\n\t\t.parse(input);\n}\n", "import type { APIThumbnailComponent } from 'discord-api-types/v10';\nimport { ComponentType } from 'discord-api-types/v10';\nimport { ComponentBuilder } from '../Component';\nimport { descriptionPredicate, spoilerPredicate, unfurledMediaItemPredicate } from './Assertions';\n\nexport class ThumbnailBuilder extends ComponentBuilder<APIThumbnailComponent> {\n\t/**\n\t * Creates a new thumbnail from API data.\n\t *\n\t * @param data - The API data to create this thumbnail with\n\t * @example\n\t * Creating a thumbnail from an API data object:\n\t * ```ts\n\t * const thumbnail = new ThumbnailBuilder({\n\t * \tdescription: 'some text',\n\t *  media: {\n\t *      url: 'https://cdn.discordapp.com/embed/avatars/4.png',\n\t *  },\n\t * });\n\t * ```\n\t * @example\n\t * Creating a thumbnail using setters and API data:\n\t * ```ts\n\t * const thumbnail = new ThumbnailBuilder({\n\t * \tmedia: {\n\t *      url: 'attachment://image.png',\n\t *  },\n\t * })\n\t * \t.setDescription('alt text');\n\t * ```\n\t */\n\tpublic constructor(data: Partial<APIThumbnailComponent> = {}) {\n\t\tsuper({\n\t\t\ttype: ComponentType.Thumbnail,\n\t\t\t...data,\n\t\t\tmedia: data.media ? { url: data.media.url } : undefined,\n\t\t});\n\t}\n\n\t/**\n\t * Sets the description of this thumbnail.\n\t *\n\t * @param description - The description to use\n\t */\n\tpublic setDescription(description: string) {\n\t\tthis.data.description = descriptionPredicate.parse(description);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Clears the description of this thumbnail.\n\t */\n\tpublic clearDescription() {\n\t\tthis.data.description = undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the spoiler status of this thumbnail.\n\t *\n\t * @param spoiler - The spoiler status to use\n\t */\n\tpublic setSpoiler(spoiler = true) {\n\t\tthis.data.spoiler = spoilerPredicate.parse(spoiler);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the media URL of this thumbnail.\n\t *\n\t * @param url - The URL to use\n\t */\n\tpublic setURL(url: string) {\n\t\tthis.data.media = unfurledMediaItemPredicate.parse({ url });\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritdoc ComponentBuilder.toJSON}\n\t */\n\tpublic override toJSON(): APIThumbnailComponent {\n\t\tunfurledMediaItemPredicate.parse(this.data.media);\n\n\t\treturn { ...this.data } as APIThumbnailComponent;\n\t}\n}\n", "import { ComponentType, type APIFileComponent } from 'discord-api-types/v10';\nimport { ComponentBuilder } from '../Component';\nimport { filePredicate, spoilerPredicate } from './Assertions';\n\nexport class FileBuilder extends ComponentBuilder<APIFileComponent> {\n\t/**\n\t * Creates a new file from API data.\n\t *\n\t * @param data - The API data to create this file with\n\t * @example\n\t * Creating a file from an API data object:\n\t * ```ts\n\t * const file = new FileBuilder({\n\t * \tspoiler: true,\n\t * \tfile: {\n\t * \t\turl: 'attachment://file.png',\n\t * \t},\n\t * });\n\t * ```\n\t * @example\n\t * Creating a file using setters and API data:\n\t * ```ts\n\t * const file = new FileBuilder({\n\t * \tfile: {\n\t * \t\turl: 'attachment://image.jpg',\n\t * \t},\n\t * })\n\t * \t.setSpoiler(false);\n\t * ```\n\t */\n\tpublic constructor(data: Partial<APIFileComponent> = {}) {\n\t\tsuper({ type: ComponentType.File, ...data, file: data.file ? { url: data.file.url } : undefined });\n\t}\n\n\t/**\n\t * Sets the spoiler status of this file.\n\t *\n\t * @param spoiler - The spoiler status to use\n\t */\n\tpublic setSpoiler(spoiler = true) {\n\t\tthis.data.spoiler = spoilerPredicate.parse(spoiler);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the media URL of this file.\n\t *\n\t * @param url - The URL to use\n\t */\n\tpublic setURL(url: string) {\n\t\tthis.data.file = filePredicate.parse({ url });\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic override toJSON(): APIFileComponent {\n\t\tfilePredicate.parse(this.data.file);\n\n\t\treturn { ...this.data, file: { ...this.data.file } } as APIFileComponent;\n\t}\n}\n", "import type { SeparatorSpacingSize, APISeparatorComponent } from 'discord-api-types/v10';\nimport { ComponentType } from 'discord-api-types/v10';\nimport { ComponentBuilder } from '../Component';\nimport { dividerPredicate, spacingPredicate } from './Assertions';\n\nexport class SeparatorBuilder extends ComponentBuilder<APISeparatorComponent> {\n\t/**\n\t * Creates a new separator from API data.\n\t *\n\t * @param data - The API data to create this separator with\n\t * @example\n\t * Creating a separator from an API data object:\n\t * ```ts\n\t * const separator = new SeparatorBuilder({\n\t * \tspacing: SeparatorSpacingSize.Small,\n\t *  divider: true,\n\t * });\n\t * ```\n\t * @example\n\t * Creating a separator using setters and API data:\n\t * ```ts\n\t * const separator = new SeparatorBuilder({\n\t * \tspacing: SeparatorSpacingSize.Large,\n\t * })\n\t * \t.setDivider(false);\n\t * ```\n\t */\n\tpublic constructor(data: Partial<APISeparatorComponent> = {}) {\n\t\tsuper({\n\t\t\ttype: ComponentType.Separator,\n\t\t\t...data,\n\t\t});\n\t}\n\n\t/**\n\t * Sets whether this separator should show a divider line.\n\t *\n\t * @param divider - Whether to show a divider line\n\t */\n\tpublic setDivider(divider = true) {\n\t\tthis.data.divider = dividerPredicate.parse(divider);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the spacing of this separator.\n\t *\n\t * @param spacing - The spacing to use\n\t */\n\tpublic setSpacing(spacing: SeparatorSpacingSize) {\n\t\tthis.data.spacing = spacingPredicate.parse(spacing);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Clears the spacing of this separator.\n\t */\n\tpublic clearSpacing() {\n\t\tthis.data.spacing = undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic override toJSON(): APISeparatorComponent {\n\t\treturn { ...this.data } as APISeparatorComponent;\n\t}\n}\n", "import type { APITextDisplayComponent } from 'discord-api-types/v10';\nimport { ComponentType } from 'discord-api-types/v10';\nimport { ComponentBuilder } from '../Component';\nimport { textDisplayContentPredicate } from './Assertions';\n\nexport class TextDisplayBuilder extends ComponentBuilder<APITextDisplayComponent> {\n\t/**\n\t * Creates a new text display from API data.\n\t *\n\t * @param data - The API data to create this text display with\n\t * @example\n\t * Creating a text display from an API data object:\n\t * ```ts\n\t * const textDisplay = new TextDisplayBuilder({\n\t * \tcontent: 'some text',\n\t * });\n\t * ```\n\t * @example\n\t * Creating a text display using setters and API data:\n\t * ```ts\n\t * const textDisplay = new TextDisplayBuilder({\n\t * \tcontent: 'old text',\n\t * })\n\t * \t.setContent('new text');\n\t * ```\n\t */\n\tpublic constructor(data: Partial<APITextDisplayComponent> = {}) {\n\t\tsuper({\n\t\t\ttype: ComponentType.TextDisplay,\n\t\t\t...data,\n\t\t});\n\t}\n\n\t/**\n\t * Sets the text of this text display.\n\t *\n\t * @param content - The text to use\n\t */\n\tpublic setContent(content: string) {\n\t\tthis.data.content = textDisplayContentPredicate.parse(content);\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic override toJSON(): APITextDisplayComponent {\n\t\ttextDisplayContentPredicate.parse(this.data.content);\n\n\t\treturn { ...this.data } as APITextDisplayComponent;\n\t}\n}\n", "/* eslint-disable jsdoc/check-param-names */\n\nimport type { APIMediaGalleryComponent, APIMediaGalleryItem } from 'discord-api-types/v10';\nimport { ComponentType } from 'discord-api-types/v10';\nimport { normalizeArray, type RestOrArray } from '../../util/normalizeArray.js';\nimport { ComponentBuilder } from '../Component.js';\nimport { resolveBuilder } from '../Components.js';\nimport { assertReturnOfBuilder, validateComponentArray } from './Assertions.js';\nimport { MediaGalleryItemBuilder } from './MediaGalleryItem.js';\n\n/**\n * A builder that creates API-compatible JSON data for a container.\n */\nexport class MediaGalleryBuilder extends ComponentBuilder<APIMediaGalleryComponent> {\n\t/**\n\t * The components within this container.\n\t */\n\tpublic readonly items: MediaGalleryItemBuilder[];\n\n\t/**\n\t * Creates a new media gallery from API data.\n\t *\n\t * @param data - The API data to create this media gallery with\n\t * @example\n\t * Creating a media gallery from an API data object:\n\t * ```ts\n\t * const mediaGallery = new MediaGalleryBuilder({\n\t * \titems: [\n\t * \t\t{\n\t * \t\t\tdescription: \"Some text here\",\n\t * \t\t\tmedia: {\n\t * \t\t\t\turl: 'https://cdn.discordapp.com/embed/avatars/2.png',\n\t * \t\t\t},\n\t * \t\t},\n\t * \t],\n\t * });\n\t * ```\n\t * @example\n\t * Creating a media gallery using setters and API data:\n\t * ```ts\n\t * const mediaGallery = new MediaGalleryBuilder({\n\t * \titems: [\n\t * \t\t{\n\t * \t\t\tdescription: \"alt text\",\n\t * \t\t\tmedia: {\n\t * \t\t\t\turl: 'https://cdn.discordapp.com/embed/avatars/5.png',\n\t * \t\t\t},\n\t * \t\t},\n\t * \t],\n\t * })\n\t * \t.addItems(item2, item3);\n\t * ```\n\t */\n\tpublic constructor({ items, ...data }: Partial<APIMediaGalleryComponent> = {}) {\n\t\tsuper({ type: ComponentType.MediaGallery, ...data });\n\t\tthis.items = items?.map((item) => new MediaGalleryItemBuilder(item)) ?? [];\n\t}\n\n\t/**\n\t * Adds items to this media gallery.\n\t *\n\t * @param items - The items to add\n\t */\n\tpublic addItems(\n\t\t...items: RestOrArray<\n\t\t\tAPIMediaGalleryItem | MediaGalleryItemBuilder | ((builder: MediaGalleryItemBuilder) => MediaGalleryItemBuilder)\n\t\t>\n\t) {\n\t\tthis.items.push(\n\t\t\t...normalizeArray(items).map((input) => {\n\t\t\t\tconst result = resolveBuilder(input, MediaGalleryItemBuilder);\n\n\t\t\t\tassertReturnOfBuilder(result, MediaGalleryItemBuilder);\n\t\t\t\treturn result;\n\t\t\t}),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Removes, replaces, or inserts media gallery items for this media gallery.\n\t *\n\t * @param index - The index to start removing, replacing or inserting items\n\t * @param deleteCount - The amount of items to remove\n\t * @param items - The items to insert\n\t */\n\tpublic spliceItems(\n\t\tindex: number,\n\t\tdeleteCount: number,\n\t\t...items: RestOrArray<\n\t\t\tAPIMediaGalleryItem | MediaGalleryItemBuilder | ((builder: MediaGalleryItemBuilder) => MediaGalleryItemBuilder)\n\t\t>\n\t) {\n\t\tthis.items.splice(\n\t\t\tindex,\n\t\t\tdeleteCount,\n\t\t\t...normalizeArray(items).map((input) => {\n\t\t\t\tconst result = resolveBuilder(input, MediaGalleryItemBuilder);\n\n\t\t\t\tassertReturnOfBuilder(result, MediaGalleryItemBuilder);\n\t\t\t\treturn result;\n\t\t\t}),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic toJSON(): APIMediaGalleryComponent {\n\t\tvalidateComponentArray(this.items, 1, 10, MediaGalleryItemBuilder);\n\t\treturn {\n\t\t\t...this.data,\n\t\t\titems: this.items.map((item) => item.toJSON()),\n\t\t} as APIMediaGalleryComponent;\n\t}\n}\n", "import type { JSONEncodable } from '@discordjs/util';\nimport type { APIMediaGalleryItem } from 'discord-api-types/v10';\nimport { descriptionPredicate, spoilerPredicate, unfurledMediaItemPredicate } from './Assertions';\n\nexport class MediaGalleryItemBuilder implements JSONEncodable<APIMediaGalleryItem> {\n\t/**\n\t * The API data associated with this media gallery item.\n\t */\n\tpublic readonly data: Partial<APIMediaGalleryItem>;\n\n\t/**\n\t * Creates a new media gallery item from API data.\n\t *\n\t * @param data - The API data to create this media gallery item with\n\t * @example\n\t * Creating a media gallery item from an API data object:\n\t * ```ts\n\t * const item = new MediaGalleryItemBuilder({\n\t * \tdescription: \"Some text here\",\n\t * \tmedia: {\n\t * \t\turl: 'https://cdn.discordapp.com/embed/avatars/2.png',\n\t * \t},\n\t * });\n\t * ```\n\t * @example\n\t * Creating a media gallery item using setters and API data:\n\t * ```ts\n\t * const item = new MediaGalleryItemBuilder({\n\t * \tmedia: {\n\t * \t\turl: 'https://cdn.discordapp.com/embed/avatars/5.png',\n\t * \t},\n\t * })\n\t * \t.setDescription(\"alt text\");\n\t * ```\n\t */\n\tpublic constructor(data: Partial<APIMediaGalleryItem> = {}) {\n\t\tthis.data = data;\n\t}\n\n\t/**\n\t * Sets the description of this media gallery item.\n\t *\n\t * @param description - The description to use\n\t */\n\tpublic setDescription(description: string) {\n\t\tthis.data.description = descriptionPredicate.parse(description);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Clears the description of this media gallery item.\n\t */\n\tpublic clearDescription() {\n\t\tthis.data.description = undefined;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the spoiler status of this media gallery item.\n\t *\n\t * @param spoiler - The spoiler status to use\n\t */\n\tpublic setSpoiler(spoiler = true) {\n\t\tthis.data.spoiler = spoilerPredicate.parse(spoiler);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the media URL of this media gallery item.\n\t *\n\t * @param url - The URL to use\n\t */\n\tpublic setURL(url: string) {\n\t\tthis.data.media = unfurledMediaItemPredicate.parse({ url });\n\t\treturn this;\n\t}\n\n\t/**\n\t * Serializes this builder to API-compatible JSON data.\n\t *\n\t * @remarks\n\t * This method runs validations on the data before serializing it.\n\t * As such, it may throw an error if the data is invalid.\n\t */\n\tpublic toJSON(): APIMediaGalleryItem {\n\t\tunfurledMediaItemPredicate.parse(this.data.media);\n\n\t\treturn { ...this.data } as APIMediaGalleryItem;\n\t}\n}\n", "/* eslint-disable jsdoc/check-param-names */\n\nimport type {\n\tAPIButtonComponent,\n\tAPISectionComponent,\n\tAPITextDisplayComponent,\n\tAPIThumbnailComponent,\n} from 'discord-api-types/v10';\nimport { ComponentType } from 'discord-api-types/v10';\nimport { ButtonBuilder, ThumbnailBuilder } from '../../index.js';\nimport { normalizeArray, type RestOrArray } from '../../util/normalizeArray.js';\nimport { ComponentBuilder } from '../Component.js';\nimport { createComponentBuilder, resolveBuilder } from '../Components.js';\nimport { accessoryPredicate, assertReturnOfBuilder, validateComponentArray } from './Assertions.js';\nimport { TextDisplayBuilder } from './TextDisplay.js';\n\n/**\n * A builder that creates API-compatible JSON data for a section.\n */\nexport class SectionBuilder extends ComponentBuilder<APISectionComponent> {\n\t/**\n\t * The components within this section.\n\t */\n\tpublic readonly components: ComponentBuilder[];\n\n\t/**\n\t * The accessory of this section.\n\t */\n\tpublic readonly accessory?: ButtonBuilder | ThumbnailBuilder;\n\n\t/**\n\t * Creates a new section from API data.\n\t *\n\t * @param data - The API data to create this section with\n\t * @example\n\t * Creating a section from an API data object:\n\t * ```ts\n\t * const section = new SectionBuilder({\n\t * \tcomponents: [\n\t * \t\t{\n\t * \t\t\tcontent: \"Some text here\",\n\t * \t\t\ttype: ComponentType.TextDisplay,\n\t * \t\t},\n\t * \t],\n\t *  accessory: {\n\t *      media: {\n\t *          url: 'https://cdn.discordapp.com/embed/avatars/3.png',\n\t *      },\n\t *  }\n\t * });\n\t * ```\n\t * @example\n\t * Creating a section using setters and API data:\n\t * ```ts\n\t * const section = new SectionBuilder({\n\t * \tcomponents: [\n\t * \t\t{\n\t * \t\t\tcontent: \"# Heading\",\n\t * \t\t\ttype: ComponentType.TextDisplay,\n\t * \t\t},\n\t * \t],\n\t * })\n\t * \t.setPrimaryButtonAccessory(button);\n\t * ```\n\t */\n\tpublic constructor({ components, accessory, ...data }: Partial<APISectionComponent> = {}) {\n\t\tsuper({ type: ComponentType.Section, ...data });\n\t\tthis.components = (components?.map((component) => createComponentBuilder(component)) ?? []) as ComponentBuilder[];\n\t\tthis.accessory = accessory ? createComponentBuilder(accessory) : undefined;\n\t}\n\n\t/**\n\t * Sets the accessory of this section to a button.\n\t *\n\t * @param accessory - The accessory to use\n\t */\n\tpublic setButtonAccessory(\n\t\taccessory: APIButtonComponent | ButtonBuilder | ((builder: ButtonBuilder) => ButtonBuilder),\n\t): this {\n\t\tReflect.set(this, 'accessory', accessoryPredicate.parse(resolveBuilder(accessory, ButtonBuilder)));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the accessory of this section to a thumbnail.\n\t *\n\t * @param accessory - The accessory to use\n\t */\n\tpublic setThumbnailAccessory(\n\t\taccessory: APIThumbnailComponent | ThumbnailBuilder | ((builder: ThumbnailBuilder) => ThumbnailBuilder),\n\t): this {\n\t\tReflect.set(this, 'accessory', accessoryPredicate.parse(resolveBuilder(accessory, ThumbnailBuilder)));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds text display components to this section.\n\t *\n\t * @param components - The text display components to add\n\t */\n\tpublic addTextDisplayComponents(\n\t\t...components: RestOrArray<TextDisplayBuilder | ((builder: TextDisplayBuilder) => TextDisplayBuilder)>\n\t) {\n\t\tthis.components.push(\n\t\t\t...normalizeArray(components).map((input) => {\n\t\t\t\tconst result = resolveBuilder(input, TextDisplayBuilder);\n\n\t\t\t\tassertReturnOfBuilder(result, TextDisplayBuilder);\n\t\t\t\treturn result;\n\t\t\t}),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Removes, replaces, or inserts text display components for this section.\n\t *\n\t * @param index - The index to start removing, replacing or inserting text display components\n\t * @param deleteCount - The amount of text display components to remove\n\t * @param components - The text display components to insert\n\t */\n\tpublic spliceTextDisplayComponents(\n\t\tindex: number,\n\t\tdeleteCount: number,\n\t\t...components: RestOrArray<\n\t\t\tAPITextDisplayComponent | TextDisplayBuilder | ((builder: TextDisplayBuilder) => TextDisplayBuilder)\n\t\t>\n\t) {\n\t\tthis.components.splice(\n\t\t\tindex,\n\t\t\tdeleteCount,\n\t\t\t...normalizeArray(components).map((input) => {\n\t\t\t\tconst result = resolveBuilder(input, TextDisplayBuilder);\n\n\t\t\t\tassertReturnOfBuilder(result, TextDisplayBuilder);\n\t\t\t\treturn result;\n\t\t\t}),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic toJSON(): APISectionComponent {\n\t\tvalidateComponentArray(this.components, 1, 3, TextDisplayBuilder);\n\t\treturn {\n\t\t\t...this.data,\n\t\t\tcomponents: this.components.map((component) => component.toJSON()),\n\t\t\taccessory: accessoryPredicate.parse(this.accessory).toJSON(),\n\t\t} as APISectionComponent;\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { ActionRowBuilder, type ModalActionRowComponentBuilder } from '../../components/ActionRow.js';\nimport { customIdValidator } from '../../components/Assertions.js';\nimport { isValidationEnabled } from '../../util/validation.js';\n\nexport const titleValidator = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(45)\n\t.setValidationEnabled(isValidationEnabled);\nexport const componentsValidator = s\n\t.instance(ActionRowBuilder)\n\t.array()\n\t.lengthGreaterThanOrEqual(1)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateRequiredParameters(\n\tcustomId?: string,\n\ttitle?: string,\n\tcomponents?: ActionRowBuilder<ModalActionRowComponentBuilder>[],\n) {\n\tcustomIdValidator.parse(customId);\n\ttitleValidator.parse(title);\n\tcomponentsValidator.parse(components);\n}\n", "/* eslint-disable jsdoc/check-param-names */\n\nimport type { JSONEncodable } from '@discordjs/util';\nimport type {\n\tAPIActionRowComponent,\n\tAPIComponentInModalActionRow,\n\tAPIModalInteractionResponseCallbackData,\n} from 'discord-api-types/v10';\nimport { ActionRowBuilder, type ModalActionRowComponentBuilder } from '../../components/ActionRow.js';\nimport { customIdValidator } from '../../components/Assertions.js';\nimport { createComponentBuilder } from '../../components/Components.js';\nimport { normalizeArray, type RestOrArray } from '../../util/normalizeArray.js';\nimport { titleValidator, validateRequiredParameters } from './Assertions.js';\n\n/**\n * A builder that creates API-compatible JSON data for modals.\n */\nexport class ModalBuilder implements JSONEncodable<APIModalInteractionResponseCallbackData> {\n\t/**\n\t * The API data associated with this modal.\n\t */\n\tpublic readonly data: Partial<APIModalInteractionResponseCallbackData>;\n\n\t/**\n\t * The components within this modal.\n\t */\n\tpublic readonly components: ActionRowBuilder<ModalActionRowComponentBuilder>[] = [];\n\n\t/**\n\t * Creates a new modal from API data.\n\t *\n\t * @param data - The API data to create this modal with\n\t */\n\tpublic constructor({ components, ...data }: Partial<APIModalInteractionResponseCallbackData> = {}) {\n\t\tthis.data = { ...data };\n\t\tthis.components = (components?.map((component) => createComponentBuilder(component)) ??\n\t\t\t[]) as ActionRowBuilder<ModalActionRowComponentBuilder>[];\n\t}\n\n\t/**\n\t * Sets the title of this modal.\n\t *\n\t * @param title - The title to use\n\t */\n\tpublic setTitle(title: string) {\n\t\tthis.data.title = titleValidator.parse(title);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the custom id of this modal.\n\t *\n\t * @param customId - The custom id to use\n\t */\n\tpublic setCustomId(customId: string) {\n\t\tthis.data.custom_id = customIdValidator.parse(customId);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds components to this modal.\n\t *\n\t * @param components - The components to add\n\t */\n\tpublic addComponents(\n\t\t...components: RestOrArray<\n\t\t\tActionRowBuilder<ModalActionRowComponentBuilder> | APIActionRowComponent<APIComponentInModalActionRow>\n\t\t>\n\t) {\n\t\tthis.components.push(\n\t\t\t...normalizeArray(components).map((component) =>\n\t\t\t\tcomponent instanceof ActionRowBuilder\n\t\t\t\t\t? component\n\t\t\t\t\t: new ActionRowBuilder<ModalActionRowComponentBuilder>(component),\n\t\t\t),\n\t\t);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets components for this modal.\n\t *\n\t * @param components - The components to set\n\t */\n\tpublic setComponents(...components: RestOrArray<ActionRowBuilder<ModalActionRowComponentBuilder>>) {\n\t\tthis.components.splice(0, this.components.length, ...normalizeArray(components));\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ComponentBuilder.toJSON}\n\t */\n\tpublic toJSON(): APIModalInteractionResponseCallbackData {\n\t\tvalidateRequiredParameters(this.data.custom_id, this.data.title, this.components);\n\n\t\treturn {\n\t\t\t...this.data,\n\t\t\tcomponents: this.components.map((component) => component.toJSON()),\n\t\t} as APIModalInteractionResponseCallbackData;\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport {\n\tApplicationIntegrationType,\n\tInteractionContextType,\n\tLocale,\n\ttype APIApplicationCommandOptionChoice,\n\ttype LocalizationMap,\n} from 'discord-api-types/v10';\nimport { isValidationEnabled } from '../../util/validation.js';\nimport type { ToAPIApplicationCommandOptions } from './SlashCommandBuilder.js';\nimport type { SlashCommandSubcommandBuilder, SlashCommandSubcommandGroupBuilder } from './SlashCommandSubcommands.js';\nimport type { ApplicationCommandOptionBase } from './mixins/ApplicationCommandOptionBase.js';\n\nconst namePredicate = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(32)\n\t.regex(/^[\\p{Ll}\\p{Lm}\\p{Lo}\\p{N}\\p{sc=Devanagari}\\p{sc=Thai}_-]+$/u)\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateName(name: unknown): asserts name is string {\n\tnamePredicate.parse(name);\n}\n\nconst descriptionPredicate = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(100)\n\t.setValidationEnabled(isValidationEnabled);\nconst localePredicate = s.nativeEnum(Locale);\n\nexport function validateDescription(description: unknown): asserts description is string {\n\tdescriptionPredicate.parse(description);\n}\n\nconst maxArrayLengthPredicate = s.unknown().array().lengthLessThanOrEqual(25).setValidationEnabled(isValidationEnabled);\nexport function validateLocale(locale: unknown) {\n\treturn localePredicate.parse(locale);\n}\n\nexport function validateMaxOptionsLength(options: unknown): asserts options is ToAPIApplicationCommandOptions[] {\n\tmaxArrayLengthPredicate.parse(options);\n}\n\nexport function validateRequiredParameters(\n\tname: string,\n\tdescription: string,\n\toptions: ToAPIApplicationCommandOptions[],\n) {\n\t// Assert name matches all conditions\n\tvalidateName(name);\n\n\t// Assert description conditions\n\tvalidateDescription(description);\n\n\t// Assert options conditions\n\tvalidateMaxOptionsLength(options);\n}\n\nconst booleanPredicate = s.boolean();\n\nexport function validateDefaultPermission(value: unknown): asserts value is boolean {\n\tbooleanPredicate.parse(value);\n}\n\nexport function validateRequired(required: unknown): asserts required is boolean {\n\tbooleanPredicate.parse(required);\n}\n\nconst choicesLengthPredicate = s.number().lessThanOrEqual(25).setValidationEnabled(isValidationEnabled);\n\nexport function validateChoicesLength(amountAdding: number, choices?: APIApplicationCommandOptionChoice[]): void {\n\tchoicesLengthPredicate.parse((choices?.length ?? 0) + amountAdding);\n}\n\nexport function assertReturnOfBuilder<\n\tReturnType extends ApplicationCommandOptionBase | SlashCommandSubcommandBuilder | SlashCommandSubcommandGroupBuilder,\n>(input: unknown, ExpectedInstanceOf: new () => ReturnType): asserts input is ReturnType {\n\ts.instance(ExpectedInstanceOf).parse(input);\n}\n\nexport const localizationMapPredicate = s\n\t.object<LocalizationMap>(Object.fromEntries(Object.values(Locale).map((locale) => [locale, s.string().nullish()])))\n\t.strict()\n\t.nullish()\n\t.setValidationEnabled(isValidationEnabled);\n\nexport function validateLocalizationMap(value: unknown): asserts value is LocalizationMap {\n\tlocalizationMapPredicate.parse(value);\n}\n\nconst dmPermissionPredicate = s.boolean().nullish();\n\nexport function validateDMPermission(value: unknown): asserts value is boolean | null | undefined {\n\tdmPermissionPredicate.parse(value);\n}\n\nconst memberPermissionPredicate = s\n\t.union([\n\t\ts.bigint().transform((value) => value.toString()),\n\t\ts\n\t\t\t.number()\n\t\t\t.safeInt()\n\t\t\t.transform((value) => value.toString()),\n\t\ts.string().regex(/^\\d+$/),\n\t])\n\t.nullish();\n\nexport function validateDefaultMemberPermissions(permissions: unknown) {\n\treturn memberPermissionPredicate.parse(permissions);\n}\n\nexport function validateNSFW(value: unknown): asserts value is boolean {\n\tbooleanPredicate.parse(value);\n}\n\nexport const contextsPredicate = s.array(\n\ts.nativeEnum(InteractionContextType).setValidationEnabled(isValidationEnabled),\n);\n\nexport const integrationTypesPredicate = s.array(\n\ts.nativeEnum(ApplicationIntegrationType).setValidationEnabled(isValidationEnabled),\n);\n", "import type {\n\tAPIApplicationCommandOption,\n\tApplicationIntegrationType,\n\tInteractionContextType,\n\tLocalizationMap,\n\tPermissions,\n} from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { SharedNameAndDescription } from './mixins/NameAndDescription.js';\nimport { SharedSlashCommand } from './mixins/SharedSlashCommand.js';\nimport { SharedSlashCommandOptions } from './mixins/SharedSlashCommandOptions.js';\nimport { SharedSlashCommandSubcommands } from './mixins/SharedSubcommands.js';\n\n/**\n * A builder that creates API-compatible JSON data for slash commands.\n */\n@mix(SharedSlashCommandOptions, SharedNameAndDescription, SharedSlashCommandSubcommands, SharedSlashCommand)\nexport class SlashCommandBuilder {\n\t/**\n\t * The name of this command.\n\t */\n\tpublic readonly name: string = undefined!;\n\n\t/**\n\t * The name localizations of this command.\n\t */\n\tpublic readonly name_localizations?: LocalizationMap;\n\n\t/**\n\t * The description of this command.\n\t */\n\tpublic readonly description: string = undefined!;\n\n\t/**\n\t * The description localizations of this command.\n\t */\n\tpublic readonly description_localizations?: LocalizationMap;\n\n\t/**\n\t * The options of this command.\n\t */\n\tpublic readonly options: ToAPIApplicationCommandOptions[] = [];\n\n\t/**\n\t * The contexts for this command.\n\t */\n\tpublic readonly contexts?: InteractionContextType[];\n\n\t/**\n\t * Whether this command is enabled by default when the application is added to a guild.\n\t *\n\t * @deprecated Use {@link SharedSlashCommand.setDefaultMemberPermissions} or {@link SharedSlashCommand.setDMPermission} instead.\n\t */\n\tpublic readonly default_permission: boolean | undefined = undefined;\n\n\t/**\n\t * The set of permissions represented as a bit set for the command.\n\t */\n\tpublic readonly default_member_permissions: Permissions | null | undefined = undefined;\n\n\t/**\n\t * Indicates whether the command is available in direct messages with the application.\n\t *\n\t * @remarks\n\t * By default, commands are visible. This property is only for global commands.\n\t * @deprecated\n\t * Use {@link SlashCommandBuilder.contexts} instead.\n\t */\n\tpublic readonly dm_permission: boolean | undefined = undefined;\n\n\t/**\n\t * The integration types for this command.\n\t */\n\tpublic readonly integration_types?: ApplicationIntegrationType[];\n\n\t/**\n\t * Whether this command is NSFW.\n\t */\n\tpublic readonly nsfw: boolean | undefined = undefined;\n}\n\nexport interface SlashCommandBuilder\n\textends SharedNameAndDescription,\n\t\tSharedSlashCommandOptions<SlashCommandOptionsOnlyBuilder>,\n\t\tSharedSlashCommandSubcommands<SlashCommandSubcommandsOnlyBuilder>,\n\t\tSharedSlashCommand {}\n\n/**\n * An interface specifically for slash command subcommands.\n */\nexport interface SlashCommandSubcommandsOnlyBuilder\n\textends SharedNameAndDescription,\n\t\tSharedSlashCommandSubcommands<SlashCommandSubcommandsOnlyBuilder>,\n\t\tSharedSlashCommand {}\n\n/**\n * An interface specifically for slash command options.\n */\nexport interface SlashCommandOptionsOnlyBuilder\n\textends SharedNameAndDescription,\n\t\tSharedSlashCommandOptions<SlashCommandOptionsOnlyBuilder>,\n\t\tSharedSlashCommand {}\n\n/**\n * An interface that ensures the `toJSON()` call will return something\n * that can be serialized into API-compatible data.\n */\nexport interface ToAPIApplicationCommandOptions {\n\ttoJSON(): APIApplicationCommandOption;\n}\n", "import type { LocaleString, LocalizationMap } from 'discord-api-types/v10';\nimport { validateDescription, validateLocale, validateName } from '../Assertions.js';\n\n/**\n * This mixin holds name and description symbols for slash commands.\n */\nexport class SharedNameAndDescription {\n\t/**\n\t * The name of this command.\n\t */\n\tpublic readonly name!: string;\n\n\t/**\n\t * The name localizations of this command.\n\t */\n\tpublic readonly name_localizations?: LocalizationMap;\n\n\t/**\n\t * The description of this command.\n\t */\n\tpublic readonly description!: string;\n\n\t/**\n\t * The description localizations of this command.\n\t */\n\tpublic readonly description_localizations?: LocalizationMap;\n\n\t/**\n\t * Sets the name of this command.\n\t *\n\t * @param name - The name to use\n\t */\n\tpublic setName(name: string): this {\n\t\t// Assert the name matches the conditions\n\t\tvalidateName(name);\n\n\t\tReflect.set(this, 'name', name);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the description of this command.\n\t *\n\t * @param description - The description to use\n\t */\n\tpublic setDescription(description: string) {\n\t\t// Assert the description matches the conditions\n\t\tvalidateDescription(description);\n\n\t\tReflect.set(this, 'description', description);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets a name localization for this command.\n\t *\n\t * @param locale - The locale to set\n\t * @param localizedName - The localized name for the given `locale`\n\t */\n\tpublic setNameLocalization(locale: LocaleString, localizedName: string | null) {\n\t\tif (!this.name_localizations) {\n\t\t\tReflect.set(this, 'name_localizations', {});\n\t\t}\n\n\t\tconst parsedLocale = validateLocale(locale);\n\n\t\tif (localizedName === null) {\n\t\t\tthis.name_localizations![parsedLocale] = null;\n\t\t\treturn this;\n\t\t}\n\n\t\tvalidateName(localizedName);\n\n\t\tthis.name_localizations![parsedLocale] = localizedName;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the name localizations for this command.\n\t *\n\t * @param localizedNames - The object of localized names to set\n\t */\n\tpublic setNameLocalizations(localizedNames: LocalizationMap | null) {\n\t\tif (localizedNames === null) {\n\t\t\tReflect.set(this, 'name_localizations', null);\n\t\t\treturn this;\n\t\t}\n\n\t\tReflect.set(this, 'name_localizations', {});\n\n\t\tfor (const args of Object.entries(localizedNames)) {\n\t\t\tthis.setNameLocalization(...(args as [LocaleString, string | null]));\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets a description localization for this command.\n\t *\n\t * @param locale - The locale to set\n\t * @param localizedDescription - The localized description for the given locale\n\t */\n\tpublic setDescriptionLocalization(locale: LocaleString, localizedDescription: string | null) {\n\t\tif (!this.description_localizations) {\n\t\t\tReflect.set(this, 'description_localizations', {});\n\t\t}\n\n\t\tconst parsedLocale = validateLocale(locale);\n\n\t\tif (localizedDescription === null) {\n\t\t\tthis.description_localizations![parsedLocale] = null;\n\t\t\treturn this;\n\t\t}\n\n\t\tvalidateDescription(localizedDescription);\n\n\t\tthis.description_localizations![parsedLocale] = localizedDescription;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the description localizations for this command.\n\t *\n\t * @param localizedDescriptions - The object of localized descriptions to set\n\t */\n\tpublic setDescriptionLocalizations(localizedDescriptions: LocalizationMap | null) {\n\t\tif (localizedDescriptions === null) {\n\t\t\tReflect.set(this, 'description_localizations', null);\n\t\t\treturn this;\n\t\t}\n\n\t\tReflect.set(this, 'description_localizations', {});\n\t\tfor (const args of Object.entries(localizedDescriptions)) {\n\t\t\tthis.setDescriptionLocalization(...(args as [LocaleString, string | null]));\n\t\t}\n\n\t\treturn this;\n\t}\n}\n", "import {\n\tApplicationCommandType,\n\ttype ApplicationIntegrationType,\n\ttype InteractionContextType,\n\ttype LocalizationMap,\n\ttype Permissions,\n\ttype RESTPostAPIChatInputApplicationCommandsJSONBody,\n} from 'discord-api-types/v10';\nimport type { RestOrArray } from '../../../util/normalizeArray.js';\nimport { normalizeArray } from '../../../util/normalizeArray.js';\nimport {\n\tcontextsPredicate,\n\tintegrationTypesPredicate,\n\tvalidateDMPermission,\n\tvalidateDefaultMemberPermissions,\n\tvalidateDefaultPermission,\n\tvalidateLocalizationMap,\n\tvalidateNSFW,\n\tvalidateRequiredParameters,\n} from '../Assertions.js';\nimport type { ToAPIApplicationCommandOptions } from '../SlashCommandBuilder.js';\n\n/**\n * This mixin holds symbols that can be shared in slashcommands independent of options or subcommands.\n */\nexport class SharedSlashCommand {\n\tpublic readonly name: string = undefined!;\n\n\tpublic readonly name_localizations?: LocalizationMap;\n\n\tpublic readonly description: string = undefined!;\n\n\tpublic readonly description_localizations?: LocalizationMap;\n\n\tpublic readonly options: ToAPIApplicationCommandOptions[] = [];\n\n\tpublic readonly contexts?: InteractionContextType[];\n\n\t/**\n\t * @deprecated Use {@link SharedSlashCommand.setDefaultMemberPermissions} or {@link SharedSlashCommand.setDMPermission} instead.\n\t */\n\tpublic readonly default_permission: boolean | undefined = undefined;\n\n\tpublic readonly default_member_permissions: Permissions | null | undefined = undefined;\n\n\t/**\n\t * @deprecated Use {@link SharedSlashCommand.contexts} instead.\n\t */\n\tpublic readonly dm_permission: boolean | undefined = undefined;\n\n\tpublic readonly integration_types?: ApplicationIntegrationType[];\n\n\tpublic readonly nsfw: boolean | undefined = undefined;\n\n\t/**\n\t * Sets the contexts of this command.\n\t *\n\t * @param contexts - The contexts\n\t */\n\tpublic setContexts(...contexts: RestOrArray<InteractionContextType>) {\n\t\tReflect.set(this, 'contexts', contextsPredicate.parse(normalizeArray(contexts)));\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the integration types of this command.\n\t *\n\t * @param integrationTypes - The integration types\n\t */\n\tpublic setIntegrationTypes(...integrationTypes: RestOrArray<ApplicationIntegrationType>) {\n\t\tReflect.set(this, 'integration_types', integrationTypesPredicate.parse(normalizeArray(integrationTypes)));\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether the command is enabled by default when the application is added to a guild.\n\t *\n\t * @remarks\n\t * If set to `false`, you will have to later `PUT` the permissions for this command.\n\t * @param value - Whether or not to enable this command by default\n\t * @see {@link https://discord.com/developers/docs/interactions/application-commands#permissions}\n\t * @deprecated Use {@link SharedSlashCommand.setDefaultMemberPermissions} or {@link SharedSlashCommand.setDMPermission} instead.\n\t */\n\tpublic setDefaultPermission(value: boolean) {\n\t\t// Assert the value matches the conditions\n\t\tvalidateDefaultPermission(value);\n\n\t\tReflect.set(this, 'default_permission', value);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the default permissions a member should have in order to run the command.\n\t *\n\t * @remarks\n\t * You can set this to `'0'` to disable the command by default.\n\t * @param permissions - The permissions bit field to set\n\t * @see {@link https://discord.com/developers/docs/interactions/application-commands#permissions}\n\t */\n\tpublic setDefaultMemberPermissions(permissions: Permissions | bigint | number | null | undefined) {\n\t\t// Assert the value and parse it\n\t\tconst permissionValue = validateDefaultMemberPermissions(permissions);\n\n\t\tReflect.set(this, 'default_member_permissions', permissionValue);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets if the command is available in direct messages with the application.\n\t *\n\t * @remarks\n\t * By default, commands are visible. This method is only for global commands.\n\t * @param enabled - Whether the command should be enabled in direct messages\n\t * @see {@link https://discord.com/developers/docs/interactions/application-commands#permissions}\n\t * @deprecated\n\t * Use {@link SharedSlashCommand.setContexts} instead.\n\t */\n\tpublic setDMPermission(enabled: boolean | null | undefined) {\n\t\t// Assert the value matches the conditions\n\t\tvalidateDMPermission(enabled);\n\n\t\tReflect.set(this, 'dm_permission', enabled);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether this command is NSFW.\n\t *\n\t * @param nsfw - Whether this command is NSFW\n\t */\n\tpublic setNSFW(nsfw = true) {\n\t\t// Assert the value matches the conditions\n\t\tvalidateNSFW(nsfw);\n\t\tReflect.set(this, 'nsfw', nsfw);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Serializes this builder to API-compatible JSON data.\n\t *\n\t * @remarks\n\t * This method runs validations on the data before serializing it.\n\t * As such, it may throw an error if the data is invalid.\n\t */\n\tpublic toJSON(): RESTPostAPIChatInputApplicationCommandsJSONBody {\n\t\tvalidateRequiredParameters(this.name, this.description, this.options);\n\n\t\tvalidateLocalizationMap(this.name_localizations);\n\t\tvalidateLocalizationMap(this.description_localizations);\n\n\t\treturn {\n\t\t\t...this,\n\t\t\ttype: ApplicationCommandType.ChatInput,\n\t\t\toptions: this.options.map((option) => option.toJSON()),\n\t\t};\n\t}\n}\n", "import { ApplicationCommandOptionType, type APIApplicationCommandAttachmentOption } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase.js';\n\n/**\n * A slash command attachment option.\n */\nexport class SlashCommandAttachmentOption extends ApplicationCommandOptionBase {\n\t/**\n\t * The type of this option.\n\t */\n\tpublic override readonly type = ApplicationCommandOptionType.Attachment as const;\n\n\t/**\n\t * {@inheritDoc ApplicationCommandOptionBase.toJSON}\n\t */\n\tpublic toJSON(): APIApplicationCommandAttachmentOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import type { APIApplicationCommandBasicOption, ApplicationCommandOptionType } from 'discord-api-types/v10';\nimport { validateRequiredParameters, validateRequired, validateLocalizationMap } from '../Assertions.js';\nimport { SharedNameAndDescription } from './NameAndDescription.js';\n\n/**\n * The base application command option builder that contains common symbols for application command builders.\n */\nexport abstract class ApplicationCommandOptionBase extends SharedNameAndDescription {\n\t/**\n\t * The type of this option.\n\t */\n\tpublic abstract readonly type: ApplicationCommandOptionType;\n\n\t/**\n\t * Whether this option is required.\n\t *\n\t * @defaultValue `false`\n\t */\n\tpublic readonly required: boolean = false;\n\n\t/**\n\t * Sets whether this option is required.\n\t *\n\t * @param required - Whether this option should be required\n\t */\n\tpublic setRequired(required: boolean) {\n\t\t// Assert that you actually passed a boolean\n\t\tvalidateRequired(required);\n\n\t\tReflect.set(this, 'required', required);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Serializes this builder to API-compatible JSON data.\n\t *\n\t * @remarks\n\t * This method runs validations on the data before serializing it.\n\t * As such, it may throw an error if the data is invalid.\n\t */\n\tpublic abstract toJSON(): APIApplicationCommandBasicOption;\n\n\t/**\n\t * This method runs required validators on this builder.\n\t */\n\tprotected runRequiredValidations() {\n\t\tvalidateRequiredParameters(this.name, this.description, []);\n\n\t\t// Validate localizations\n\t\tvalidateLocalizationMap(this.name_localizations);\n\t\tvalidateLocalizationMap(this.description_localizations);\n\n\t\t// Assert that you actually passed a boolean\n\t\tvalidateRequired(this.required);\n\t}\n}\n", "import { ApplicationCommandOptionType, type APIApplicationCommandBooleanOption } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase.js';\n\n/**\n * A slash command boolean option.\n */\nexport class SlashCommandBooleanOption extends ApplicationCommandOptionBase {\n\t/**\n\t * The type of this option.\n\t */\n\tpublic readonly type = ApplicationCommandOptionType.Boolean as const;\n\n\t/**\n\t * {@inheritDoc ApplicationCommandOptionBase.toJSON}\n\t */\n\tpublic toJSON(): APIApplicationCommandBooleanOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import { ApplicationCommandOptionType, type APIApplicationCommandChannelOption } from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase.js';\nimport { ApplicationCommandOptionChannelTypesMixin } from '../mixins/ApplicationCommandOptionChannelTypesMixin.js';\n\n/**\n * A slash command channel option.\n */\n@mix(ApplicationCommandOptionChannelTypesMixin)\nexport class SlashCommandChannelOption extends ApplicationCommandOptionBase {\n\t/**\n\t * The type of this option.\n\t */\n\tpublic override readonly type = ApplicationCommandOptionType.Channel as const;\n\n\t/**\n\t * {@inheritDoc ApplicationCommandOptionBase.toJSON}\n\t */\n\tpublic toJSON(): APIApplicationCommandChannelOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n\nexport interface SlashCommandChannelOption extends ApplicationCommandOptionChannelTypesMixin {}\n", "import { s } from '@sapphire/shapeshift';\nimport { ChannelType } from 'discord-api-types/v10';\nimport { normalizeArray, type RestOrArray } from '../../../util/normalizeArray';\n\n/**\n * The allowed channel types used for a channel option in a slash command builder.\n *\n * @privateRemarks This can't be dynamic because const enums are erased at runtime.\n * @internal\n */\nconst allowedChannelTypes = [\n\tChannelType.GuildText,\n\tChannelType.GuildVoice,\n\tChannelType.GuildCategory,\n\tChannelType.GuildAnnouncement,\n\tChannelType.AnnouncementThread,\n\tChannelType.PublicThread,\n\tChannelType.PrivateThread,\n\tChannelType.GuildStageVoice,\n\tChannelType.GuildForum,\n\tChannelType.GuildMedia,\n] as const;\n\n/**\n * The type of allowed channel types used for a channel option.\n */\nexport type ApplicationCommandOptionAllowedChannelTypes = (typeof allowedChannelTypes)[number];\n\nconst channelTypesPredicate = s.array(s.union(allowedChannelTypes.map((type) => s.literal(type))));\n\n/**\n * This mixin holds channel type symbols used for options.\n */\nexport class ApplicationCommandOptionChannelTypesMixin {\n\t/**\n\t * The channel types of this option.\n\t */\n\tpublic readonly channel_types?: ApplicationCommandOptionAllowedChannelTypes[];\n\n\t/**\n\t * Adds channel types to this option.\n\t *\n\t * @param channelTypes - The channel types\n\t */\n\tpublic addChannelTypes(...channelTypes: RestOrArray<ApplicationCommandOptionAllowedChannelTypes>) {\n\t\tif (this.channel_types === undefined) {\n\t\t\tReflect.set(this, 'channel_types', []);\n\t\t}\n\n\t\tthis.channel_types!.push(...channelTypesPredicate.parse(normalizeArray(channelTypes)));\n\n\t\treturn this;\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { ApplicationCommandOptionType, type APIApplicationCommandIntegerOption } from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { ApplicationCommandNumericOptionMinMaxValueMixin } from '../mixins/ApplicationCommandNumericOptionMinMaxValueMixin.js';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase.js';\nimport { ApplicationCommandOptionWithAutocompleteMixin } from '../mixins/ApplicationCommandOptionWithAutocompleteMixin.js';\nimport { ApplicationCommandOptionWithChoicesMixin } from '../mixins/ApplicationCommandOptionWithChoicesMixin.js';\n\nconst numberValidator = s.number().int();\n\n/**\n * A slash command integer option.\n */\n@mix(\n\tApplicationCommandNumericOptionMinMaxValueMixin,\n\tApplicationCommandOptionWithAutocompleteMixin,\n\tApplicationCommandOptionWithChoicesMixin,\n)\nexport class SlashCommandIntegerOption\n\textends ApplicationCommandOptionBase\n\timplements ApplicationCommandNumericOptionMinMaxValueMixin\n{\n\t/**\n\t * The type of this option.\n\t */\n\tpublic readonly type = ApplicationCommandOptionType.Integer as const;\n\n\t/**\n\t * {@inheritDoc ApplicationCommandNumericOptionMinMaxValueMixin.setMaxValue}\n\t */\n\tpublic setMaxValue(max: number): this {\n\t\tnumberValidator.parse(max);\n\n\t\tReflect.set(this, 'max_value', max);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ApplicationCommandNumericOptionMinMaxValueMixin.setMinValue}\n\t */\n\tpublic setMinValue(min: number): this {\n\t\tnumberValidator.parse(min);\n\n\t\tReflect.set(this, 'min_value', min);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ApplicationCommandOptionBase.toJSON}\n\t */\n\tpublic toJSON(): APIApplicationCommandIntegerOption {\n\t\tthis.runRequiredValidations();\n\n\t\tif (this.autocomplete && Array.isArray(this.choices) && this.choices.length > 0) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\treturn { ...this } as APIApplicationCommandIntegerOption;\n\t}\n}\n\nexport interface SlashCommandIntegerOption\n\textends ApplicationCommandNumericOptionMinMaxValueMixin,\n\t\tApplicationCommandOptionWithChoicesMixin<number>,\n\t\tApplicationCommandOptionWithAutocompleteMixin {}\n", "/**\n * This mixin holds minimum and maximum symbols used for options.\n */\nexport abstract class ApplicationCommandNumericOptionMinMaxValueMixin {\n\t/**\n\t * The maximum value of this option.\n\t */\n\tpublic readonly max_value?: number;\n\n\t/**\n\t * The minimum value of this option.\n\t */\n\tpublic readonly min_value?: number;\n\n\t/**\n\t * Sets the maximum number value of this option.\n\t *\n\t * @param max - The maximum value this option can be\n\t */\n\tpublic abstract setMaxValue(max: number): this;\n\n\t/**\n\t * Sets the minimum number value of this option.\n\t *\n\t * @param min - The minimum value this option can be\n\t */\n\tpublic abstract setMinValue(min: number): this;\n}\n", "import { s } from '@sapphire/shapeshift';\nimport type { ApplicationCommandOptionType } from 'discord-api-types/v10';\n\nconst booleanPredicate = s.boolean();\n\n/**\n * This mixin holds choices and autocomplete symbols used for options.\n */\nexport class ApplicationCommandOptionWithAutocompleteMixin {\n\t/**\n\t * Whether this option utilizes autocomplete.\n\t */\n\tpublic readonly autocomplete?: boolean;\n\n\t/**\n\t * The type of this option.\n\t *\n\t * @privateRemarks Since this is present and this is a mixin, this is needed.\n\t */\n\tpublic readonly type!: ApplicationCommandOptionType;\n\n\t/**\n\t * Whether this option uses autocomplete.\n\t *\n\t * @param autocomplete - Whether this option should use autocomplete\n\t */\n\tpublic setAutocomplete(autocomplete: boolean): this {\n\t\t// Assert that you actually passed a boolean\n\t\tbooleanPredicate.parse(autocomplete);\n\n\t\tif (autocomplete && 'choices' in this && Array.isArray(this.choices) && this.choices.length > 0) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\tReflect.set(this, 'autocomplete', autocomplete);\n\n\t\treturn this;\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { ApplicationCommandOptionType, type APIApplicationCommandOptionChoice } from 'discord-api-types/v10';\nimport { normalizeArray, type RestOrArray } from '../../../util/normalizeArray.js';\nimport { localizationMapPredicate, validateChoicesLength } from '../Assertions.js';\n\nconst stringPredicate = s.string().lengthGreaterThanOrEqual(1).lengthLessThanOrEqual(100);\nconst numberPredicate = s.number().greaterThan(Number.NEGATIVE_INFINITY).lessThan(Number.POSITIVE_INFINITY);\nconst choicesPredicate = s\n\t.object({\n\t\tname: stringPredicate,\n\t\tname_localizations: localizationMapPredicate,\n\t\tvalue: s.union([stringPredicate, numberPredicate]),\n\t})\n\t.array();\n\n/**\n * This mixin holds choices and autocomplete symbols used for options.\n */\nexport class ApplicationCommandOptionWithChoicesMixin<ChoiceType extends number | string> {\n\t/**\n\t * The choices of this option.\n\t */\n\tpublic readonly choices?: APIApplicationCommandOptionChoice<ChoiceType>[];\n\n\t/**\n\t * The type of this option.\n\t *\n\t * @privateRemarks Since this is present and this is a mixin, this is needed.\n\t */\n\tpublic readonly type!: ApplicationCommandOptionType;\n\n\t/**\n\t * Adds multiple choices to this option.\n\t *\n\t * @param choices - The choices to add\n\t */\n\tpublic addChoices(...choices: RestOrArray<APIApplicationCommandOptionChoice<ChoiceType>>): this {\n\t\tconst normalizedChoices = normalizeArray(choices);\n\t\tif (normalizedChoices.length > 0 && 'autocomplete' in this && this.autocomplete) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\tchoicesPredicate.parse(normalizedChoices);\n\n\t\tif (this.choices === undefined) {\n\t\t\tReflect.set(this, 'choices', []);\n\t\t}\n\n\t\tvalidateChoicesLength(normalizedChoices.length, this.choices);\n\n\t\tfor (const { name, name_localizations, value } of normalizedChoices) {\n\t\t\t// Validate the value\n\t\t\tif (this.type === ApplicationCommandOptionType.String) {\n\t\t\t\tstringPredicate.parse(value);\n\t\t\t} else {\n\t\t\t\tnumberPredicate.parse(value);\n\t\t\t}\n\n\t\t\tthis.choices!.push({ name, name_localizations, value });\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets multiple choices for this option.\n\t *\n\t * @param choices - The choices to set\n\t */\n\tpublic setChoices<Input extends APIApplicationCommandOptionChoice<ChoiceType>>(...choices: RestOrArray<Input>): this {\n\t\tconst normalizedChoices = normalizeArray(choices);\n\t\tif (normalizedChoices.length > 0 && 'autocomplete' in this && this.autocomplete) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\tchoicesPredicate.parse(normalizedChoices);\n\n\t\tReflect.set(this, 'choices', []);\n\t\tthis.addChoices(normalizedChoices);\n\n\t\treturn this;\n\t}\n}\n", "import { ApplicationCommandOptionType, type APIApplicationCommandMentionableOption } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase.js';\n\n/**\n * A slash command mentionable option.\n */\nexport class SlashCommandMentionableOption extends ApplicationCommandOptionBase {\n\t/**\n\t * The type of this option.\n\t */\n\tpublic readonly type = ApplicationCommandOptionType.Mentionable as const;\n\n\t/**\n\t * {@inheritDoc ApplicationCommandOptionBase.toJSON}\n\t */\n\tpublic toJSON(): APIApplicationCommandMentionableOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { ApplicationCommandOptionType, type APIApplicationCommandNumberOption } from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { ApplicationCommandNumericOptionMinMaxValueMixin } from '../mixins/ApplicationCommandNumericOptionMinMaxValueMixin.js';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase.js';\nimport { ApplicationCommandOptionWithAutocompleteMixin } from '../mixins/ApplicationCommandOptionWithAutocompleteMixin.js';\nimport { ApplicationCommandOptionWithChoicesMixin } from '../mixins/ApplicationCommandOptionWithChoicesMixin.js';\n\nconst numberValidator = s.number();\n\n/**\n * A slash command number option.\n */\n@mix(\n\tApplicationCommandNumericOptionMinMaxValueMixin,\n\tApplicationCommandOptionWithAutocompleteMixin,\n\tApplicationCommandOptionWithChoicesMixin,\n)\nexport class SlashCommandNumberOption\n\textends ApplicationCommandOptionBase\n\timplements ApplicationCommandNumericOptionMinMaxValueMixin\n{\n\t/**\n\t * The type of this option.\n\t */\n\tpublic readonly type = ApplicationCommandOptionType.Number as const;\n\n\t/**\n\t * {@inheritDoc ApplicationCommandNumericOptionMinMaxValueMixin.setMaxValue}\n\t */\n\tpublic setMaxValue(max: number): this {\n\t\tnumberValidator.parse(max);\n\n\t\tReflect.set(this, 'max_value', max);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ApplicationCommandNumericOptionMinMaxValueMixin.setMinValue}\n\t */\n\tpublic setMinValue(min: number): this {\n\t\tnumberValidator.parse(min);\n\n\t\tReflect.set(this, 'min_value', min);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ApplicationCommandOptionBase.toJSON}\n\t */\n\tpublic toJSON(): APIApplicationCommandNumberOption {\n\t\tthis.runRequiredValidations();\n\n\t\tif (this.autocomplete && Array.isArray(this.choices) && this.choices.length > 0) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\treturn { ...this } as APIApplicationCommandNumberOption;\n\t}\n}\n\nexport interface SlashCommandNumberOption\n\textends ApplicationCommandNumericOptionMinMaxValueMixin,\n\t\tApplicationCommandOptionWithChoicesMixin<number>,\n\t\tApplicationCommandOptionWithAutocompleteMixin {}\n", "import { ApplicationCommandOptionType, type APIApplicationCommandRoleOption } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase.js';\n\n/**\n * A slash command role option.\n */\nexport class SlashCommandRoleOption extends ApplicationCommandOptionBase {\n\t/**\n\t * The type of this option.\n\t */\n\tpublic override readonly type = ApplicationCommandOptionType.Role as const;\n\n\t/**\n\t * {@inheritDoc ApplicationCommandOptionBase.toJSON}\n\t */\n\tpublic toJSON(): APIApplicationCommandRoleOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { ApplicationCommandOptionType, type APIApplicationCommandStringOption } from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase.js';\nimport { ApplicationCommandOptionWithAutocompleteMixin } from '../mixins/ApplicationCommandOptionWithAutocompleteMixin.js';\nimport { ApplicationCommandOptionWithChoicesMixin } from '../mixins/ApplicationCommandOptionWithChoicesMixin.js';\n\nconst minLengthValidator = s.number().greaterThanOrEqual(0).lessThanOrEqual(6_000);\nconst maxLengthValidator = s.number().greaterThanOrEqual(1).lessThanOrEqual(6_000);\n\n/**\n * A slash command string option.\n */\n@mix(ApplicationCommandOptionWithAutocompleteMixin, ApplicationCommandOptionWithChoicesMixin)\nexport class SlashCommandStringOption extends ApplicationCommandOptionBase {\n\t/**\n\t * The type of this option.\n\t */\n\tpublic readonly type = ApplicationCommandOptionType.String as const;\n\n\t/**\n\t * The maximum length of this option.\n\t */\n\tpublic readonly max_length?: number;\n\n\t/**\n\t * The minimum length of this option.\n\t */\n\tpublic readonly min_length?: number;\n\n\t/**\n\t * Sets the maximum length of this string option.\n\t *\n\t * @param max - The maximum length this option can be\n\t */\n\tpublic setMaxLength(max: number): this {\n\t\tmaxLengthValidator.parse(max);\n\n\t\tReflect.set(this, 'max_length', max);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the minimum length of this string option.\n\t *\n\t * @param min - The minimum length this option can be\n\t */\n\tpublic setMinLength(min: number): this {\n\t\tminLengthValidator.parse(min);\n\n\t\tReflect.set(this, 'min_length', min);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * {@inheritDoc ApplicationCommandOptionBase.toJSON}\n\t */\n\tpublic toJSON(): APIApplicationCommandStringOption {\n\t\tthis.runRequiredValidations();\n\n\t\tif (this.autocomplete && Array.isArray(this.choices) && this.choices.length > 0) {\n\t\t\tthrow new RangeError('Autocomplete and choices are mutually exclusive to each other.');\n\t\t}\n\n\t\treturn { ...this } as APIApplicationCommandStringOption;\n\t}\n}\n\nexport interface SlashCommandStringOption\n\textends ApplicationCommandOptionWithChoicesMixin<string>,\n\t\tApplicationCommandOptionWithAutocompleteMixin {}\n", "import { ApplicationCommandOptionType, type APIApplicationCommandUserOption } from 'discord-api-types/v10';\nimport { ApplicationCommandOptionBase } from '../mixins/ApplicationCommandOptionBase.js';\n\n/**\n * A slash command user option.\n */\nexport class SlashCommandUserOption extends ApplicationCommandOptionBase {\n\t/**\n\t * The type of this option.\n\t */\n\tpublic readonly type = ApplicationCommandOptionType.User as const;\n\n\t/**\n\t * {@inheritDoc ApplicationCommandOptionBase.toJSON}\n\t */\n\tpublic toJSON(): APIApplicationCommandUserOption {\n\t\tthis.runRequiredValidations();\n\n\t\treturn { ...this };\n\t}\n}\n", "import { assertReturnOfBuilder, validateMaxOptionsLength } from '../Assertions.js';\nimport type { ToAPIApplicationCommandOptions } from '../SlashCommandBuilder';\nimport { SlashCommandAttachmentOption } from '../options/attachment.js';\nimport { SlashCommandBooleanOption } from '../options/boolean.js';\nimport { SlashCommandChannelOption } from '../options/channel.js';\nimport { SlashCommandIntegerOption } from '../options/integer.js';\nimport { SlashCommandMentionableOption } from '../options/mentionable.js';\nimport { SlashCommandNumberOption } from '../options/number.js';\nimport { SlashCommandRoleOption } from '../options/role.js';\nimport { SlashCommandStringOption } from '../options/string.js';\nimport { SlashCommandUserOption } from '../options/user.js';\nimport type { ApplicationCommandOptionBase } from './ApplicationCommandOptionBase.js';\n\n/**\n * This mixin holds symbols that can be shared in slash command options.\n *\n * @typeParam TypeAfterAddingOptions - The type this class should return after adding an option.\n */\nexport class SharedSlashCommandOptions<\n\tTypeAfterAddingOptions extends SharedSlashCommandOptions<TypeAfterAddingOptions>,\n> {\n\tpublic readonly options!: ToAPIApplicationCommandOptions[];\n\n\t/**\n\t * Adds a boolean option.\n\t *\n\t * @param input - A function that returns an option builder or an already built builder\n\t */\n\tpublic addBooleanOption(\n\t\tinput: SlashCommandBooleanOption | ((builder: SlashCommandBooleanOption) => SlashCommandBooleanOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandBooleanOption);\n\t}\n\n\t/**\n\t * Adds a user option.\n\t *\n\t * @param input - A function that returns an option builder or an already built builder\n\t */\n\tpublic addUserOption(input: SlashCommandUserOption | ((builder: SlashCommandUserOption) => SlashCommandUserOption)) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandUserOption);\n\t}\n\n\t/**\n\t * Adds a channel option.\n\t *\n\t * @param input - A function that returns an option builder or an already built builder\n\t */\n\tpublic addChannelOption(\n\t\tinput: SlashCommandChannelOption | ((builder: SlashCommandChannelOption) => SlashCommandChannelOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandChannelOption);\n\t}\n\n\t/**\n\t * Adds a role option.\n\t *\n\t * @param input - A function that returns an option builder or an already built builder\n\t */\n\tpublic addRoleOption(input: SlashCommandRoleOption | ((builder: SlashCommandRoleOption) => SlashCommandRoleOption)) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandRoleOption);\n\t}\n\n\t/**\n\t * Adds an attachment option.\n\t *\n\t * @param input - A function that returns an option builder or an already built builder\n\t */\n\tpublic addAttachmentOption(\n\t\tinput: SlashCommandAttachmentOption | ((builder: SlashCommandAttachmentOption) => SlashCommandAttachmentOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandAttachmentOption);\n\t}\n\n\t/**\n\t * Adds a mentionable option.\n\t *\n\t * @param input - A function that returns an option builder or an already built builder\n\t */\n\tpublic addMentionableOption(\n\t\tinput: SlashCommandMentionableOption | ((builder: SlashCommandMentionableOption) => SlashCommandMentionableOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandMentionableOption);\n\t}\n\n\t/**\n\t * Adds a string option.\n\t *\n\t * @param input - A function that returns an option builder or an already built builder\n\t */\n\tpublic addStringOption(\n\t\tinput: SlashCommandStringOption | ((builder: SlashCommandStringOption) => SlashCommandStringOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandStringOption);\n\t}\n\n\t/**\n\t * Adds an integer option.\n\t *\n\t * @param input - A function that returns an option builder or an already built builder\n\t */\n\tpublic addIntegerOption(\n\t\tinput: SlashCommandIntegerOption | ((builder: SlashCommandIntegerOption) => SlashCommandIntegerOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandIntegerOption);\n\t}\n\n\t/**\n\t * Adds a number option.\n\t *\n\t * @param input - A function that returns an option builder or an already built builder\n\t */\n\tpublic addNumberOption(\n\t\tinput: SlashCommandNumberOption | ((builder: SlashCommandNumberOption) => SlashCommandNumberOption),\n\t) {\n\t\treturn this._sharedAddOptionMethod(input, SlashCommandNumberOption);\n\t}\n\n\t/**\n\t * Where the actual adding magic happens. ✨\n\t *\n\t * @param input - The input. What else?\n\t * @param Instance - The instance of whatever is being added\n\t * @internal\n\t */\n\tprivate _sharedAddOptionMethod<OptionBuilder extends ApplicationCommandOptionBase>(\n\t\tinput: OptionBuilder | ((builder: OptionBuilder) => OptionBuilder),\n\t\tInstance: new () => OptionBuilder,\n\t): TypeAfterAddingOptions {\n\t\tconst { options } = this;\n\n\t\t// First, assert options conditions - we cannot have more than 25 options\n\t\tvalidateMaxOptionsLength(options);\n\n\t\t// Get the final result\n\t\tconst result = typeof input === 'function' ? input(new Instance()) : input;\n\n\t\tassertReturnOfBuilder(result, Instance);\n\n\t\t// Push it\n\t\toptions.push(result);\n\n\t\treturn this as unknown as TypeAfterAddingOptions;\n\t}\n}\n", "import {\n\tApplicationCommandOptionType,\n\ttype APIApplicationCommandSubcommandGroupOption,\n\ttype APIApplicationCommandSubcommandOption,\n} from 'discord-api-types/v10';\nimport { mix } from 'ts-mixer';\nimport { assertReturnOfBuilder, validateMaxOptionsLength, validateRequiredParameters } from './Assertions.js';\nimport type { ToAPIApplicationCommandOptions } from './SlashCommandBuilder.js';\nimport type { ApplicationCommandOptionBase } from './mixins/ApplicationCommandOptionBase.js';\nimport { SharedNameAndDescription } from './mixins/NameAndDescription.js';\nimport { SharedSlashCommandOptions } from './mixins/SharedSlashCommandOptions.js';\n\n/**\n * Represents a folder for subcommands.\n *\n * @see {@link https://discord.com/developers/docs/interactions/application-commands#subcommands-and-subcommand-groups}\n */\n@mix(SharedNameAndDescription)\nexport class Slash<PERSON>ommandSubcommandGroupBuilder implements ToAPIApplicationCommandOptions {\n\t/**\n\t * The name of this subcommand group.\n\t */\n\tpublic readonly name: string = undefined!;\n\n\t/**\n\t * The description of this subcommand group.\n\t */\n\tpublic readonly description: string = undefined!;\n\n\t/**\n\t * The subcommands within this subcommand group.\n\t */\n\tpublic readonly options: SlashCommandSubcommandBuilder[] = [];\n\n\t/**\n\t * Adds a new subcommand to this group.\n\t *\n\t * @param input - A function that returns a subcommand builder or an already built builder\n\t */\n\tpublic addSubcommand(\n\t\tinput:\n\t\t\t| SlashCommandSubcommandBuilder\n\t\t\t| ((subcommandGroup: SlashCommandSubcommandBuilder) => SlashCommandSubcommandBuilder),\n\t) {\n\t\tconst { options } = this;\n\n\t\t// First, assert options conditions - we cannot have more than 25 options\n\t\tvalidateMaxOptionsLength(options);\n\n\t\t// Get the final result\n\t\t// eslint-disable-next-line @typescript-eslint/no-use-before-define\n\t\tconst result = typeof input === 'function' ? input(new SlashCommandSubcommandBuilder()) : input;\n\n\t\t// eslint-disable-next-line @typescript-eslint/no-use-before-define\n\t\tassertReturnOfBuilder(result, SlashCommandSubcommandBuilder);\n\n\t\t// Push it\n\t\toptions.push(result);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Serializes this builder to API-compatible JSON data.\n\t *\n\t * @remarks\n\t * This method runs validations on the data before serializing it.\n\t * As such, it may throw an error if the data is invalid.\n\t */\n\tpublic toJSON(): APIApplicationCommandSubcommandGroupOption {\n\t\tvalidateRequiredParameters(this.name, this.description, this.options);\n\n\t\treturn {\n\t\t\ttype: ApplicationCommandOptionType.SubcommandGroup,\n\t\t\tname: this.name,\n\t\t\tname_localizations: this.name_localizations,\n\t\t\tdescription: this.description,\n\t\t\tdescription_localizations: this.description_localizations,\n\t\t\toptions: this.options.map((option) => option.toJSON()),\n\t\t};\n\t}\n}\n\nexport interface SlashCommandSubcommandGroupBuilder extends SharedNameAndDescription {}\n\n/**\n * A builder that creates API-compatible JSON data for slash command subcommands.\n *\n * @see {@link https://discord.com/developers/docs/interactions/application-commands#subcommands-and-subcommand-groups}\n */\n@mix(SharedNameAndDescription, SharedSlashCommandOptions)\nexport class SlashCommandSubcommandBuilder implements ToAPIApplicationCommandOptions {\n\t/**\n\t * The name of this subcommand.\n\t */\n\tpublic readonly name: string = undefined!;\n\n\t/**\n\t * The description of this subcommand.\n\t */\n\tpublic readonly description: string = undefined!;\n\n\t/**\n\t * The options within this subcommand.\n\t */\n\tpublic readonly options: ApplicationCommandOptionBase[] = [];\n\n\t/**\n\t * Serializes this builder to API-compatible JSON data.\n\t *\n\t * @remarks\n\t * This method runs validations on the data before serializing it.\n\t * As such, it may throw an error if the data is invalid.\n\t */\n\tpublic toJSON(): APIApplicationCommandSubcommandOption {\n\t\tvalidateRequiredParameters(this.name, this.description, this.options);\n\n\t\treturn {\n\t\t\ttype: ApplicationCommandOptionType.Subcommand,\n\t\t\tname: this.name,\n\t\t\tname_localizations: this.name_localizations,\n\t\t\tdescription: this.description,\n\t\t\tdescription_localizations: this.description_localizations,\n\t\t\toptions: this.options.map((option) => option.toJSON()),\n\t\t};\n\t}\n}\n\nexport interface SlashCommandSubcommandBuilder\n\textends SharedNameAndDescription,\n\t\tSharedSlashCommandOptions<SlashCommandSubcommandBuilder> {}\n", "import { assertReturnOfBuilder, validateMaxOptionsLength } from '../Assertions.js';\nimport type { ToAPIApplicationCommandOptions } from '../SlashCommandBuilder.js';\nimport { SlashCommandSubcommandBuilder, SlashCommandSubcommandGroupBuilder } from '../SlashCommandSubcommands.js';\n\n/**\n * This mixin holds symbols that can be shared in slash subcommands.\n *\n * @typeParam TypeAfterAddingSubcommands - The type this class should return after adding a subcommand or subcommand group.\n */\nexport class SharedSlashCommandSubcommands<\n\tTypeAfterAddingSubcommands extends SharedSlashCommandSubcommands<TypeAfterAddingSubcommands>,\n> {\n\tpublic readonly options: ToAPIApplicationCommandOptions[] = [];\n\n\t/**\n\t * Adds a new subcommand group to this command.\n\t *\n\t * @param input - A function that returns a subcommand group builder or an already built builder\n\t */\n\tpublic addSubcommandGroup(\n\t\tinput:\n\t\t\t| SlashCommandSubcommandGroupBuilder\n\t\t\t| ((subcommandGroup: SlashCommandSubcommandGroupBuilder) => SlashCommandSubcommandGroupBuilder),\n\t): TypeAfterAddingSubcommands {\n\t\tconst { options } = this;\n\n\t\t// First, assert options conditions - we cannot have more than 25 options\n\t\tvalidateMaxOptionsLength(options);\n\n\t\t// Get the final result\n\t\tconst result = typeof input === 'function' ? input(new SlashCommandSubcommandGroupBuilder()) : input;\n\n\t\tassertReturnOfBuilder(result, SlashCommandSubcommandGroupBuilder);\n\n\t\t// Push it\n\t\toptions.push(result);\n\n\t\treturn this as unknown as TypeAfterAddingSubcommands;\n\t}\n\n\t/**\n\t * Adds a new subcommand to this command.\n\t *\n\t * @param input - A function that returns a subcommand builder or an already built builder\n\t */\n\tpublic addSubcommand(\n\t\tinput:\n\t\t\t| SlashCommandSubcommandBuilder\n\t\t\t| ((subcommandGroup: SlashCommandSubcommandBuilder) => SlashCommandSubcommandBuilder),\n\t): TypeAfterAddingSubcommands {\n\t\tconst { options } = this;\n\n\t\t// First, assert options conditions - we cannot have more than 25 options\n\t\tvalidateMaxOptionsLength(options);\n\n\t\t// Get the final result\n\t\tconst result = typeof input === 'function' ? input(new SlashCommandSubcommandBuilder()) : input;\n\n\t\tassertReturnOfBuilder(result, SlashCommandSubcommandBuilder);\n\n\t\t// Push it\n\t\toptions.push(result);\n\n\t\treturn this as unknown as TypeAfterAddingSubcommands;\n\t}\n}\n", "import { s } from '@sapphire/shapeshift';\nimport { ApplicationCommandType, ApplicationIntegrationType, InteractionContextType } from 'discord-api-types/v10';\nimport { isValidationEnabled } from '../../util/validation.js';\nimport type { ContextMenuCommandType } from './ContextMenuCommandBuilder.js';\n\nconst namePredicate = s\n\t.string()\n\t.lengthGreaterThanOrEqual(1)\n\t.lengthLessThanOrEqual(32)\n\t.regex(/\\S/)\n\t.setValidationEnabled(isValidationEnabled);\nconst typePredicate = s\n\t.union([s.literal(ApplicationCommandType.User), s.literal(ApplicationCommandType.Message)])\n\t.setValidationEnabled(isValidationEnabled);\nconst booleanPredicate = s.boolean();\n\nexport function validateDefaultPermission(value: unknown): asserts value is boolean {\n\tbooleanPredicate.parse(value);\n}\n\nexport function validateName(name: unknown): asserts name is string {\n\tnamePredicate.parse(name);\n}\n\nexport function validateType(type: unknown): asserts type is ContextMenuCommandType {\n\ttypePredicate.parse(type);\n}\n\nexport function validateRequiredParameters(name: string, type: number) {\n\t// Assert name matches all conditions\n\tvalidateName(name);\n\n\t// Assert type is valid\n\tvalidateType(type);\n}\n\nconst dmPermissionPredicate = s.boolean().nullish();\n\nexport function validateDMPermission(value: unknown): asserts value is boolean | null | undefined {\n\tdmPermissionPredicate.parse(value);\n}\n\nconst memberPermissionPredicate = s\n\t.union([\n\t\ts.bigint().transform((value) => value.toString()),\n\t\ts\n\t\t\t.number()\n\t\t\t.safeInt()\n\t\t\t.transform((value) => value.toString()),\n\t\ts.string().regex(/^\\d+$/),\n\t])\n\t.nullish();\n\nexport function validateDefaultMemberPermissions(permissions: unknown) {\n\treturn memberPermissionPredicate.parse(permissions);\n}\n\nexport const contextsPredicate = s.array(\n\ts.nativeEnum(InteractionContextType).setValidationEnabled(isValidationEnabled),\n);\n\nexport const integrationTypesPredicate = s.array(\n\ts.nativeEnum(ApplicationIntegrationType).setValidationEnabled(isValidationEnabled),\n);\n", "import type {\n\tApplicationCommandType,\n\tApplicationIntegrationType,\n\tInteractionContextType,\n\tLocaleString,\n\tLocalizationMap,\n\tPermissions,\n\tRESTPostAPIContextMenuApplicationCommandsJSONBody,\n} from 'discord-api-types/v10';\nimport type { RestOrArray } from '../../util/normalizeArray.js';\nimport { normalizeArray } from '../../util/normalizeArray.js';\nimport { validateLocale, validateLocalizationMap } from '../slashCommands/Assertions.js';\nimport {\n\tvalidateRequiredParameters,\n\tvalidateName,\n\tvalidateType,\n\tvalidateDefaultPermission,\n\tvalidateDefaultMemberPermissions,\n\tvalidateDMPermission,\n\tcontextsPredicate,\n\tintegrationTypesPredicate,\n} from './Assertions.js';\n\n/**\n * The type a context menu command can be.\n */\nexport type ContextMenuCommandType = ApplicationCommandType.Message | ApplicationCommandType.User;\n\n/**\n * A builder that creates API-compatible JSON data for context menu commands.\n */\nexport class ContextMenuCommandBuilder {\n\t/**\n\t * The name of this command.\n\t */\n\tpublic readonly name: string = undefined!;\n\n\t/**\n\t * The name localizations of this command.\n\t */\n\tpublic readonly name_localizations?: LocalizationMap;\n\n\t/**\n\t * The type of this command.\n\t */\n\tpublic readonly type: ContextMenuCommandType = undefined!;\n\n\t/**\n\t * The contexts for this command.\n\t */\n\tpublic readonly contexts?: InteractionContextType[];\n\n\t/**\n\t * Whether this command is enabled by default when the application is added to a guild.\n\t *\n\t * @deprecated Use {@link ContextMenuCommandBuilder.setDefaultMemberPermissions} or {@link ContextMenuCommandBuilder.setDMPermission} instead.\n\t */\n\tpublic readonly default_permission: boolean | undefined = undefined;\n\n\t/**\n\t * The set of permissions represented as a bit set for the command.\n\t */\n\tpublic readonly default_member_permissions: Permissions | null | undefined = undefined;\n\n\t/**\n\t * Indicates whether the command is available in direct messages with the application.\n\t *\n\t * @remarks\n\t * By default, commands are visible. This property is only for global commands.\n\t * @deprecated\n\t * Use {@link ContextMenuCommandBuilder.contexts} instead.\n\t */\n\tpublic readonly dm_permission: boolean | undefined = undefined;\n\n\t/**\n\t * The integration types for this command.\n\t */\n\tpublic readonly integration_types?: ApplicationIntegrationType[];\n\n\t/**\n\t * Sets the contexts of this command.\n\t *\n\t * @param contexts - The contexts\n\t */\n\tpublic setContexts(...contexts: RestOrArray<InteractionContextType>) {\n\t\tReflect.set(this, 'contexts', contextsPredicate.parse(normalizeArray(contexts)));\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets integration types of this command.\n\t *\n\t * @param integrationTypes - The integration types\n\t */\n\tpublic setIntegrationTypes(...integrationTypes: RestOrArray<ApplicationIntegrationType>) {\n\t\tReflect.set(this, 'integration_types', integrationTypesPredicate.parse(normalizeArray(integrationTypes)));\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the name of this command.\n\t *\n\t * @param name - The name to use\n\t */\n\tpublic setName(name: string) {\n\t\t// Assert the name matches the conditions\n\t\tvalidateName(name);\n\n\t\tReflect.set(this, 'name', name);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the type of this command.\n\t *\n\t * @param type - The type to use\n\t */\n\tpublic setType(type: ContextMenuCommandType) {\n\t\t// Assert the type is valid\n\t\tvalidateType(type);\n\n\t\tReflect.set(this, 'type', type);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether the command is enabled by default when the application is added to a guild.\n\t *\n\t * @remarks\n\t * If set to `false`, you will have to later `PUT` the permissions for this command.\n\t * @param value - Whether to enable this command by default\n\t * @see {@link https://discord.com/developers/docs/interactions/application-commands#permissions}\n\t * @deprecated Use {@link ContextMenuCommandBuilder.setDefaultMemberPermissions} or {@link ContextMenuCommandBuilder.setDMPermission} instead.\n\t */\n\tpublic setDefaultPermission(value: boolean) {\n\t\t// Assert the value matches the conditions\n\t\tvalidateDefaultPermission(value);\n\n\t\tReflect.set(this, 'default_permission', value);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the default permissions a member should have in order to run this command.\n\t *\n\t * @remarks\n\t * You can set this to `'0'` to disable the command by default.\n\t * @param permissions - The permissions bit field to set\n\t * @see {@link https://discord.com/developers/docs/interactions/application-commands#permissions}\n\t */\n\tpublic setDefaultMemberPermissions(permissions: Permissions | bigint | number | null | undefined) {\n\t\t// Assert the value and parse it\n\t\tconst permissionValue = validateDefaultMemberPermissions(permissions);\n\n\t\tReflect.set(this, 'default_member_permissions', permissionValue);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets if the command is available in direct messages with the application.\n\t *\n\t * @remarks\n\t * By default, commands are visible. This method is only for global commands.\n\t * @param enabled - Whether the command should be enabled in direct messages\n\t * @see {@link https://discord.com/developers/docs/interactions/application-commands#permissions}\n\t * @deprecated Use {@link ContextMenuCommandBuilder.setContexts} instead.\n\t */\n\tpublic setDMPermission(enabled: boolean | null | undefined) {\n\t\t// Assert the value matches the conditions\n\t\tvalidateDMPermission(enabled);\n\n\t\tReflect.set(this, 'dm_permission', enabled);\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets a name localization for this command.\n\t *\n\t * @param locale - The locale to set\n\t * @param localizedName - The localized name for the given `locale`\n\t */\n\tpublic setNameLocalization(locale: LocaleString, localizedName: string | null) {\n\t\tif (!this.name_localizations) {\n\t\t\tReflect.set(this, 'name_localizations', {});\n\t\t}\n\n\t\tconst parsedLocale = validateLocale(locale);\n\n\t\tif (localizedName === null) {\n\t\t\tthis.name_localizations![parsedLocale] = null;\n\t\t\treturn this;\n\t\t}\n\n\t\tvalidateName(localizedName);\n\n\t\tthis.name_localizations![parsedLocale] = localizedName;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the name localizations for this command.\n\t *\n\t * @param localizedNames - The object of localized names to set\n\t */\n\tpublic setNameLocalizations(localizedNames: LocalizationMap | null) {\n\t\tif (localizedNames === null) {\n\t\t\tReflect.set(this, 'name_localizations', null);\n\t\t\treturn this;\n\t\t}\n\n\t\tReflect.set(this, 'name_localizations', {});\n\n\t\tfor (const args of Object.entries(localizedNames))\n\t\t\tthis.setNameLocalization(...(args as [LocaleString, string | null]));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Serializes this builder to API-compatible JSON data.\n\t *\n\t * @remarks\n\t * This method runs validations on the data before serializing it.\n\t * As such, it may throw an error if the data is invalid.\n\t */\n\tpublic toJSON(): RESTPostAPIContextMenuApplicationCommandsJSONBody {\n\t\tvalidateRequiredParameters(this.name, this.type);\n\n\t\tvalidateLocalizationMap(this.name_localizations);\n\n\t\treturn { ...this };\n\t}\n}\n", "import type { APIEmbed } from 'discord-api-types/v10';\n\n/**\n * Calculates the length of the embed.\n *\n * @param data - The embed data to check\n */\nexport function embedLength(data: APIEmbed) {\n\treturn (\n\t\t(data.title?.length ?? 0) +\n\t\t(data.description?.length ?? 0) +\n\t\t(data.fields?.reduce((prev, curr) => prev + curr.name.length + curr.value.length, 0) ?? 0) +\n\t\t(data.footer?.text.length ?? 0) +\n\t\t(data.author?.name.length ?? 0)\n\t);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAS,SAAS;;;ACAlB,IAAI,WAAW;AAOR,SAAS,mBAAmB;AAClC,SAAQ,WAAW;AACpB;AAFgB;AAST,SAAS,oBAAoB;AACnC,SAAQ,WAAW;AACpB;AAFgB;AAOT,SAAS,sBAAsB;AACrC,SAAO;AACR;AAFgB;;;ADnBT,IAAM,qBAAqB,EAAE,OAAO,EAAE,sBAAsB,GAAG,EAAE,qBAAqB,mBAAmB;AAEzG,IAAM,sBAAsB,EAAE,OAAO,EAAE,sBAAsB,IAAK,EAAE,qBAAqB,mBAAmB;AAE5G,IAAM,uBAAuB,EAAE,QAAQ,EAAE,SAAS;AAElD,IAAM,sBAAsB,EACjC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACT,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,IAAM,4BAA4B,oBAAoB,MAAM,EAAE,qBAAqB,mBAAmB;AAEtG,IAAM,uBAAuB,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAAE,qBAAqB,mBAAmB;AAEpG,SAAS,oBAAoB,cAAsB,QAAgC;AACzF,uBAAqB,OAAO,QAAQ,UAAU,KAAK,YAAY;AAChE;AAFgB;AAIT,IAAM,sBAAsB,mBACjC,yBAAyB,CAAC,EAC1B,SAAS,EACT,qBAAqB,mBAAmB;AAEnC,IAAM,oBAAoB,EAC/B,OAAO,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,SAAS,UAAU,aAAa;AACpD,CAAC,EACA,QAAQ,EACR,qBAAqB,mBAAmB;AAEnC,IAAM,eAAe,EAC1B,OAAO,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,SAAS,QAAQ;AACrC,CAAC,EACA,QAAQ,EACR,qBAAqB,mBAAmB;AAEnC,IAAM,uBAAuB,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AAAA,EACT,KAAK;AACN,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,IAAM,eAAe,EAC1B,OAAO,EACP,IAAI,EACJ,mBAAmB,CAAC,EACpB,gBAAgB,GAAG,EACnB,qBAAqB,mBAAmB;AACnC,IAAM,iBAAiB,EAC5B,OAAO,EACP,IAAI,EACJ,mBAAmB,CAAC,EACpB,gBAAgB,QAAQ,EACxB,GAAG,EAAE,MAAM,CAAC,cAAc,cAAc,YAAY,CAAC,CAAC,EACtD,SAAS,EACT,qBAAqB,mBAAmB;AAEnC,IAAM,uBAAuB,EAClC,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,IAAK,EAC3B,SAAS,EACT,qBAAqB,mBAAmB;AAEnC,IAAM,sBAAsB,EACjC,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,IAAK,EAC3B,SAAS,EACT,qBAAqB,mBAAmB;AAEnC,IAAM,uBAAuB,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AACV,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,IAAM,qBAAqB,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,SAAS,EAAE,qBAAqB,mBAAmB;AAE9G,IAAM,iBAAiB,mBAC5B,yBAAyB,CAAC,EAC1B,SAAS,EACT,qBAAqB,mBAAmB;;;AE1FnC,SAAS,eAAyB,KAAwC;AAChF,MAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,EAAG,QAAO,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO;AACR;AAHgB;;;AC+DT,IAAM,eAAN,MAAmB;AAAA,EArE1B,OAqE0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,YAAY,OAAiB,CAAC,GAAG;AACvC,SAAK,OAAO,EAAE,GAAG,KAAK;AACtB,QAAI,KAAK,UAAW,MAAK,KAAK,YAAY,IAAI,KAAK,KAAK,SAAS,EAAE,YAAY;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BO,aAAa,QAA0C;AAC7D,UAAM,mBAAmB,eAAe,MAAM;AAE9C,wBAAoB,iBAAiB,QAAQ,KAAK,KAAK,MAAM;AAG7D,8BAA0B,MAAM,gBAAgB;AAEhD,QAAI,KAAK,KAAK,OAAQ,MAAK,KAAK,OAAO,KAAK,GAAG,gBAAgB;AAAA,QAC1D,MAAK,KAAK,SAAS;AACxB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BO,aAAa,OAAe,gBAAwB,QAA+B;AAEzF,wBAAoB,OAAO,SAAS,aAAa,KAAK,KAAK,MAAM;AAGjE,8BAA0B,MAAM,MAAM;AACtC,QAAI,KAAK,KAAK,OAAQ,MAAK,KAAK,OAAO,OAAO,OAAO,aAAa,GAAG,MAAM;AAAA,QACtE,MAAK,KAAK,SAAS;AACxB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYO,aAAa,QAA0C;AAC7D,SAAK,aAAa,GAAG,KAAK,KAAK,QAAQ,UAAU,GAAG,GAAG,eAAe,MAAM,CAAC;AAC7E,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,UAAU,SAA0C;AAC1D,QAAI,YAAY,MAAM;AACrB,WAAK,KAAK,SAAS;AACnB,aAAO;AAAA,IACR;AAGA,yBAAqB,MAAM,OAAO;AAElC,SAAK,KAAK,SAAS,EAAE,MAAM,QAAQ,MAAM,KAAK,QAAQ,KAAK,UAAU,QAAQ,QAAQ;AACrF,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAuC;AAEtD,mBAAe,MAAM,KAAK;AAE1B,QAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,CAAC,KAAK,OAAO,IAAI,IAAI;AAC3B,WAAK,KAAK,SAAS,OAAO,OAAO,SAAS,KAAK;AAC/C,aAAO;AAAA,IACR;AAEA,SAAK,KAAK,QAAQ,SAAS;AAC3B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,aAAkC;AAEvD,yBAAqB,MAAM,WAAW;AAEtC,SAAK,KAAK,cAAc,eAAe;AACvC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,UAAU,SAA0C;AAC1D,QAAI,YAAY,MAAM;AACrB,WAAK,KAAK,SAAS;AACnB,aAAO;AAAA,IACR;AAGA,yBAAqB,MAAM,OAAO;AAElC,SAAK,KAAK,SAAS,EAAE,MAAM,QAAQ,MAAM,UAAU,QAAQ,QAAQ;AACnE,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,KAA0B;AAEzC,sBAAkB,MAAM,GAAG;AAE3B,SAAK,KAAK,QAAQ,MAAM,EAAE,IAAI,IAAI;AAClC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,aAAa,KAA0B;AAE7C,sBAAkB,MAAM,GAAG;AAE3B,SAAK,KAAK,YAAY,MAAM,EAAE,IAAI,IAAI;AACtC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,aAAa,YAAkC,KAAK,IAAI,GAAS;AAEvE,uBAAmB,MAAM,SAAS;AAElC,SAAK,KAAK,YAAY,YAAY,IAAI,KAAK,SAAS,EAAE,YAAY,IAAI;AACtE,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAA4B;AAE3C,mBAAe,MAAM,KAAK;AAE1B,SAAK,KAAK,QAAQ,SAAS;AAC3B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,OAAO,KAA0B;AAEvC,iBAAa,MAAM,GAAG;AAEtB,SAAK,KAAK,MAAM,OAAO;AACvB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,SAAmB;AACzB,WAAO,EAAE,GAAG,KAAK,KAAK;AAAA,EACvB;AACD;;;AC9TA,cAAc;;;ACHd,IAAAA,sBAAA;AAAA,SAAAA,qBAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAS,KAAAC,UAAS;AAClB,SAAS,aAAa,mBAAkD;;;ACWjE,IAAM,gCAAN,MAAkF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBjF,YAAmB,OAAqC,CAAC,GAAG;AAAzC;AAAA,EAA0C;AAAA,EAnCrE,OAYyF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BjF,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,+BAA+B,MAAM,KAAK;AAC5D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,+BAA+B,MAAM,KAAK;AAC5D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,aAAqB;AAC1C,SAAK,KAAK,cAAc,+BAA+B,MAAM,WAAW;AACxE,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,YAAY,MAAM;AACnC,SAAK,KAAK,UAAU,iBAAiB,MAAM,SAAS;AACpD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAiC;AAChD,SAAK,KAAK,QAAQ,eAAe,MAAM,KAAK;AAC5C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAA8B;AACpC,+CAA2C,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAE3E,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,IACT;AAAA,EACD;AACD;;;AD5FO,IAAM,cAAcC,GACzB,OAAO,EACP,QAAQ,EACR,mBAAmB,CAAC,EACpB,SAAS,UAAa,EACtB,qBAAqB,mBAAmB;AAEnC,IAAM,oBAAoBA,GAC/B,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,GAAG,EACzB,qBAAqB,mBAAmB;AAEnC,IAAM,iBAAiBA,GAC5B,OAAO;AAAA,EACP,IAAIA,GAAE,OAAO;AAAA,EACb,MAAMA,GAAE,OAAO;AAAA,EACf,UAAUA,GAAE,QAAQ;AACrB,CAAC,EACA,QAAQ,EACR,OAAO,EACP,qBAAqB,mBAAmB;AAEnC,IAAM,oBAAoBA,GAAE,QAAQ;AAEpC,IAAM,uBAAuBA,GAClC,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,qBAAqB,mBAAmB;AAEnC,IAAM,uBAAuBA,GAAE,WAAW,WAAW;AAErD,IAAM,uBAAuBA,GAAE,OAAO,EAAE,sBAAsB,GAAG,EAAE,qBAAqB,mBAAmB;AAC3G,IAAM,kBAAkBA,GAC7B,OAAO,EACP,IAAI,EACJ,mBAAmB,CAAC,EACpB,gBAAgB,EAAE,EAClB,qBAAqB,mBAAmB;AAEnC,IAAM,iCAAiCA,GAC5C,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,GAAG,EACzB,qBAAqB,mBAAmB;AAEnC,IAAM,sBAAsBA,GACjC,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,aAAa,+BAA+B,SAAS;AAAA,EACrD,OAAO,eAAe,SAAS;AAAA,EAC/B,SAASA,GAAE,QAAQ,EAAE,SAAS;AAC/B,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,IAAM,kBAAkBA,GAAE,SAAS,6BAA6B,EAAE,qBAAqB,mBAAmB;AAE1G,IAAM,mBAAmB,gBAC9B,MAAM,EACN,yBAAyB,CAAC,EAC1B,qBAAqB,mBAAmB;AACnC,IAAM,yBAAyBA,GACpC,OAAO,EACP,IAAI,EACJ,mBAAmB,CAAC,EACpB,gBAAgB,EAAE,EAClB,qBAAqB,mBAAmB;AAEnC,SAAS,qCAAqC,SAA0C,UAAmB;AACjH,oBAAkB,MAAM,QAAQ;AAChC,mBAAiB,MAAM,OAAO;AAC/B;AAHgB;AAKT,IAAM,mBAAmBA,GAAE,QAAQ;AAEnC,SAAS,2CAA2C,OAAgB,OAAgB;AAC1F,iCAA+B,MAAM,KAAK;AAC1C,iCAA+B,MAAM,KAAK;AAC3C;AAHgB;AAKT,IAAM,wBAAwBA,GAAE,WAAW,WAAW,EAAE,MAAM,EAAE,qBAAqB,mBAAmB;AAExG,IAAM,eAAeA,GAC1B,OAAO,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,SAAS,UAAU,UAAU;AACjD,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,SAAS,iCACf,OACA,OACA,OACA,UACA,OACA,KACC;AACD,MAAI,UAAU,YAAY,SAAS;AAClC,QAAI,CAAC,OAAO;AACX,YAAM,IAAI,WAAW,sCAAsC;AAAA,IAC5D;AAEA,QAAI,YAAY,SAAS,OAAO,OAAO;AACtC,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAAA,EACD,OAAO;AACN,QAAI,OAAO;AACV,YAAM,IAAI,WAAW,8CAA8C;AAAA,IACpE;AAEA,QAAI,OAAO,UAAU;AACpB,YAAM,IAAI,WAAW,2CAA2C;AAAA,IACjE;AAEA,QAAI,CAAC,SAAS,CAAC,OAAO;AACrB,YAAM,IAAI,WAAW,wDAAwD;AAAA,IAC9E;AAEA,QAAI,UAAU,YAAY,MAAM;AAC/B,UAAI,CAAC,KAAK;AACT,cAAM,IAAI,WAAW,+BAA+B;AAAA,MACrD;AAAA,IACD,WAAW,KAAK;AACf,YAAM,IAAI,WAAW,qDAAqD;AAAA,IAC3E;AAAA,EACD;AACD;AArCgB;;;AE9FhB;AAAA,EAEC,iBAAAC;AAAA,OAIM;;;ACeA,IAAe,mBAAf,MAGP;AAAA,EA1BA,OA0BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBT,YAAY,MAAyB;AAC3C,SAAK,OAAO;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,MAAM,IAAY;AACxB,SAAK,KAAK,KAAK,YAAY,MAAM,EAAE;AACnC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,UAAU;AAChB,SAAK,KAAK,KAAK;AACf,WAAO;AAAA,EACR;AACD;;;AClEA,SAAS,iBAAAC,uBAAuE;;;ACDhF;AAAA,EACC;AAAA,OAQM;AAeA,IAAM,gBAAN,cAA4B,iBAAqC;AAAA,EAxBxE,OAwBwE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BhE,YAAY,MAAoC;AACtD,UAAM,EAAE,MAAM,cAAc,QAAQ,GAAG,KAAK,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAoB;AACnC,SAAK,KAAK,QAAQ,qBAAqB,MAAM,KAAK;AAClD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,OAAO,KAAa;AAC1B,IAAC,KAAK,KAAmC,MAAM,aAAa,MAAM,GAAG;AACrE,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,YAAY,UAAkB;AACpC,IAAC,KAAK,KAAwC,YAAY,kBAAkB,MAAM,QAAQ;AAC1F,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,SAAS,OAAkB;AACjC,IAAC,KAAK,KAAqC,SAAS;AACpD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAiC;AAChD,IAAC,KAAK,KAAkE,QAAQ,eAAe,MAAM,KAAK;AAC1G,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,YAAY,WAAW,MAAM;AACnC,SAAK,KAAK,WAAW,kBAAkB,MAAM,QAAQ;AACrD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAe;AAC9B,IAAC,KAAK,KAAkE,QAAQ,qBAAqB,MAAM,KAAK;AAChH,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAA6B;AACnC;AAAA,MACC,KAAK,KAAK;AAAA,MACT,KAAK,KAAkE;AAAA,MACvE,KAAK,KAAkE;AAAA,MACvE,KAAK,KAAwC;AAAA,MAC7C,KAAK,KAAqC;AAAA,MAC1C,KAAK,KAAmC;AAAA,IAC1C;AAEA,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,IACT;AAAA,EACD;AACD;;;ACtJA;AAAA,EAIC,iBAAAC;AAAA,EACA;AAAA,OACM;;;ACGA,IAAe,wBAAf,cAEG,iBAAiC;AAAA,EAX3C,OAW2C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,eAAe,aAAqB;AAC1C,SAAK,KAAK,cAAc,qBAAqB,MAAM,WAAW;AAC9D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,aAAa,WAAmB;AACtC,SAAK,KAAK,aAAa,gBAAgB,MAAM,SAAS;AACtD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,aAAa,WAAmB;AACtC,SAAK,KAAK,aAAa,gBAAgB,MAAM,SAAS;AACtD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,YAAY,UAAkB;AACpC,SAAK,KAAK,YAAY,kBAAkB,MAAM,QAAQ;AACtD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,YAAY,WAAW,MAAM;AACnC,SAAK,KAAK,WAAW,kBAAkB,MAAM,QAAQ;AACrD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAAyB;AAC/B,sBAAkB,MAAM,KAAK,KAAK,SAAS;AAC3C,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,IACT;AAAA,EACD;AACD;;;ADzDO,IAAM,2BAAN,cAAuC,sBAAiD;AAAA,EAd/F,OAc+F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBvF,YAAY,MAA2C;AAC7D,UAAM,EAAE,GAAG,MAAM,MAAMC,eAAc,cAAc,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBAAmB,OAAiC;AAC1D,UAAM,kBAAkB,eAAe,KAAK;AAC5C,SAAK,KAAK,kBAAkB,CAAC;AAC7B,SAAK,KAAK,cAAc,KAAK,GAAG,sBAAsB,MAAM,eAAe,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBAAmB,OAAiC;AAC1D,UAAM,kBAAkB,eAAe,KAAK;AAC5C,SAAK,KAAK,kBAAkB,CAAC;AAC7B,SAAK,KAAK,cAAc,OAAO,GAAG,KAAK,KAAK,cAAc,QAAQ,GAAG,sBAAsB,MAAM,eAAe,CAAC;AACjH,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,sBAAsB,UAAkC;AAC9D,UAAM,mBAAmB,eAAe,QAAQ;AAChD,2BAAuB,OAAO,KAAK,KAAK,gBAAgB,UAAU,KAAK,iBAAiB,MAAM;AAC9F,SAAK,KAAK,mBAAmB,CAAC;AAE9B,SAAK,KAAK,eAAe;AAAA,MACxB,GAAG,iBAAiB,IAAI,CAAC,QAAQ;AAAA,QAChC;AAAA,QACA,MAAM,2BAA2B;AAAA,MAClC,EAAE;AAAA,IACH;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,sBAAsB,UAAkC;AAC9D,UAAM,mBAAmB,eAAe,QAAQ;AAChD,2BAAuB,MAAM,iBAAiB,MAAM;AAEpD,SAAK,KAAK,iBAAiB,iBAAiB,IAAI,CAAC,QAAQ;AAAA,MACxD;AAAA,MACA,MAAM,2BAA2B;AAAA,IAClC,EAAE;AAEF,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKgB,SAAoC;AACnD,sBAAkB,MAAM,KAAK,KAAK,SAAS;AAE3C,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,IACT;AAAA,EACD;AACD;;;AEjHA;AAAA,EAIC,iBAAAC;AAAA,EACA,8BAAAC;AAAA,OACM;AAQA,IAAM,+BAAN,cAA2C,sBAAqD;AAAA,EAdvG,OAcuG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuB/F,YAAY,MAA+C;AACjE,UAAM,EAAE,GAAG,MAAM,MAAMC,eAAc,kBAAkB,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBAAmB,OAA+B;AACxD,UAAM,mBAAmB,eAAe,KAAK;AAC7C,2BAAuB,OAAO,KAAK,KAAK,gBAAgB,UAAU,KAAK,iBAAiB,MAAM;AAC9F,SAAK,KAAK,mBAAmB,CAAC;AAE9B,SAAK,KAAK,eAAe;AAAA,MACxB,GAAG,iBAAiB,IAAI,CAAC,QAAQ;AAAA,QAChC;AAAA,QACA,MAAMC,4BAA2B;AAAA,MAClC,EAAE;AAAA,IACH;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBAAmB,OAA+B;AACxD,UAAM,mBAAmB,eAAe,KAAK;AAC7C,2BAAuB,OAAO,KAAK,KAAK,gBAAgB,UAAU,KAAK,iBAAiB,MAAM;AAC9F,SAAK,KAAK,mBAAmB,CAAC;AAE9B,SAAK,KAAK,eAAe;AAAA,MACxB,GAAG,iBAAiB,IAAI,CAAC,QAAQ;AAAA,QAChC;AAAA,QACA,MAAMA,4BAA2B;AAAA,MAClC,EAAE;AAAA,IACH;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,oBACH,QAIF;AACD,UAAM,mBAAmB,eAAe,MAAM;AAC9C,2BAAuB,OAAO,KAAK,KAAK,gBAAgB,UAAU,KAAK,iBAAiB,MAAM;AAC9F,SAAK,KAAK,mBAAmB,CAAC;AAC9B,SAAK,KAAK,eAAe,KAAK,GAAG,gBAAgB;AACjD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,oBACH,QAIF;AACD,UAAM,mBAAmB,eAAe,MAAM;AAC9C,2BAAuB,MAAM,iBAAiB,MAAM;AACpD,SAAK,KAAK,iBAAiB;AAC3B,WAAO;AAAA,EACR;AACD;;;ACnHA;AAAA,EAGC,iBAAAC;AAAA,EACA,8BAAAC;AAAA,OACM;AAQA,IAAM,wBAAN,cAAoC,sBAA8C;AAAA,EAbzF,OAayF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBjF,YAAY,MAAwC;AAC1D,UAAM,EAAE,GAAG,MAAM,MAAMC,eAAc,WAAW,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBAAmB,OAA+B;AACxD,UAAM,mBAAmB,eAAe,KAAK;AAC7C,2BAAuB,OAAO,KAAK,KAAK,gBAAgB,UAAU,KAAK,iBAAiB,MAAM;AAC9F,SAAK,KAAK,mBAAmB,CAAC;AAE9B,SAAK,KAAK,eAAe;AAAA,MACxB,GAAG,iBAAiB,IAAI,CAAC,QAAQ;AAAA,QAChC;AAAA,QACA,MAAMC,4BAA2B;AAAA,MAClC,EAAE;AAAA,IACH;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBAAmB,OAA+B;AACxD,UAAM,mBAAmB,eAAe,KAAK;AAC7C,2BAAuB,MAAM,iBAAiB,MAAM;AAEpD,SAAK,KAAK,iBAAiB,iBAAiB,IAAI,CAAC,QAAQ;AAAA,MACxD;AAAA,MACA,MAAMA,4BAA2B;AAAA,IAClC,EAAE;AAEF,WAAO;AAAA,EACR;AACD;;;AC5EA,SAAS,iBAAAC,sBAAqB;AAUvB,IAAM,0BAAN,cAAsC,sBAAgD;AAAA,EAV7F,OAU6F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiCT,YAAY,MAA0C;AAC5D,UAAM,EAAE,SAAS,GAAG,SAAS,IAAI,QAAQ,CAAC;AAC1C,UAAM,EAAE,GAAG,UAAU,MAAMC,eAAc,aAAa,CAAC;AACvD,SAAK,UAAU,SAAS,IAAI,CAAC,WAAgC,IAAI,8BAA8B,MAAM,CAAC,KAAK,CAAC;AAAA,EAC7G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,SAA2E;AAC/F,UAAM,oBAAoB,eAAe,OAAO;AAChD,2BAAuB,MAAM,KAAK,QAAQ,SAAS,kBAAkB,MAAM;AAC3E,SAAK,QAAQ;AAAA,MACZ,GAAG,kBAAkB;AAAA,QAAI,CAAC,qBACzB,4BAA4B,gCACzB,mBACA,IAAI,8BAA8B,oBAAoB,MAAM,gBAAgB,CAAC;AAAA,MACjF;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,SAA2E;AAC/F,WAAO,KAAK,cAAc,GAAG,KAAK,QAAQ,QAAQ,GAAG,OAAO;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BO,cACN,OACA,gBACG,SACF;AACD,UAAM,oBAAoB,eAAe,OAAO;AAEhD,UAAM,QAAQ,CAAC,GAAG,KAAK,OAAO;AAE9B,UAAM;AAAA,MACL;AAAA,MACA;AAAA,MACA,GAAG,kBAAkB;AAAA,QAAI,CAAC,qBACzB,4BAA4B,gCACzB,mBACA,IAAI,8BAA8B,oBAAoB,MAAM,gBAAgB,CAAC;AAAA,MACjF;AAAA,IACD;AAEA,2BAAuB,MAAM,MAAM,MAAM;AACzC,SAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,QAAQ,GAAG,KAAK;AACpD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKgB,SAAmC;AAClD,yCAAqC,KAAK,SAAS,KAAK,KAAK,SAAS;AAEtE,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,MACR,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,CAAC;AAAA,IACtD;AAAA,EACD;AACD;;;AC9IA;AAAA,EAGC,iBAAAC;AAAA,EACA,8BAAAC;AAAA,OACM;AAQA,IAAM,wBAAN,cAAoC,sBAA8C;AAAA,EAbzF,OAayF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBjF,YAAY,MAAwC;AAC1D,UAAM,EAAE,GAAG,MAAM,MAAMC,eAAc,WAAW,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBAAmB,OAA+B;AACxD,UAAM,mBAAmB,eAAe,KAAK;AAC7C,2BAAuB,OAAO,KAAK,KAAK,gBAAgB,UAAU,KAAK,iBAAiB,MAAM;AAC9F,SAAK,KAAK,mBAAmB,CAAC;AAE9B,SAAK,KAAK,eAAe;AAAA,MACxB,GAAG,iBAAiB,IAAI,CAAC,QAAQ;AAAA,QAChC;AAAA,QACA,MAAMC,4BAA2B;AAAA,MAClC,EAAE;AAAA,IACH;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBAAmB,OAA+B;AACxD,UAAM,mBAAmB,eAAe,KAAK;AAC7C,2BAAuB,MAAM,iBAAiB,MAAM;AAEpD,SAAK,KAAK,iBAAiB,iBAAiB,IAAI,CAAC,QAAQ;AAAA,MACxD;AAAA,MACA,MAAMA,4BAA2B;AAAA,IAClC,EAAE;AAEF,WAAO;AAAA,EACR;AACD;;;AC5EA,SAAS,uBAA2D;AACpE,SAAS,iBAAAC,sBAAsE;AAC/E,OAAO,aAAa;;;ACFpB,IAAAC,sBAAA;AAAA,SAAAA,qBAAA;AAAA;AAAA;AAAA;AAAA,8BAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAS,KAAAC,UAAS;AAClB,SAAS,sBAAsB;AAIxB,IAAM,0BAA0BC,GAAE,WAAW,cAAc;AAC3D,IAAM,qBAAqBA,GAChC,OAAO,EACP,IAAI,EACJ,mBAAmB,CAAC,EACpB,gBAAgB,GAAK,EACrB,qBAAqB,mBAAmB;AACnC,IAAM,qBAAqBA,GAChC,OAAO,EACP,IAAI,EACJ,mBAAmB,CAAC,EACpB,gBAAgB,GAAK,EACrB,qBAAqB,mBAAmB;AACnC,IAAM,oBAAoBA,GAAE,QAAQ;AACpC,IAAM,iBAAiBA,GAAE,OAAO,EAAE,sBAAsB,GAAK,EAAE,qBAAqB,mBAAmB;AACvG,IAAMC,wBAAuBD,GAAE,OAAO,EAAE,sBAAsB,GAAG,EAAE,qBAAqB,mBAAmB;AAC3G,IAAM,iBAAiBA,GAC5B,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,qBAAqB,mBAAmB;AAEnC,SAAS,2BAA2B,UAAmB,OAAwB,OAAgB;AACrG,oBAAkB,MAAM,QAAQ;AAChC,0BAAwB,MAAM,KAAK;AACnC,iBAAe,MAAM,KAAK;AAC3B;AAJgB;;;ADRT,IAAM,mBAAN,cACE,iBAET;AAAA,EAtBA,OAsBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBQ,YAAY,MAAmE;AACrF,UAAM,EAAE,MAAME,eAAc,WAAW,GAAG,KAAK,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,YAAY,UAAkB;AACpC,SAAK,KAAK,YAAY,kBAAkB,MAAM,QAAQ;AACtD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,eAAe,MAAM,KAAK;AAC5C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAuB;AACtC,SAAK,KAAK,QAAQ,wBAAwB,MAAM,KAAK;AACrD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,aAAa,WAAmB;AACtC,SAAK,KAAK,aAAa,mBAAmB,MAAM,SAAS;AACzD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,aAAa,WAAmB;AACtC,SAAK,KAAK,aAAa,mBAAmB,MAAM,SAAS;AACzD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,aAAqB;AAC1C,SAAK,KAAK,cAAcC,sBAAqB,MAAM,WAAW;AAC9D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,eAAe,MAAM,KAAK;AAC5C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,YAAY,WAAW,MAAM;AACnC,SAAK,KAAK,WAAW,kBAAkB,MAAM,QAAQ;AACrD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAAgC;AACtC,+BAA2B,KAAK,KAAK,WAAW,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAEhF,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,IACT;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKO,OAAO,OAA8E;AAC3F,QAAI,gBAAgB,KAAK,GAAG;AAC3B,aAAO,QAAQ,MAAM,OAAO,GAAG,KAAK,IAAI;AAAA,IACzC;AAEA,WAAO,QAAQ,OAAO,KAAK,IAAI;AAAA,EAChC;AACD;;;AE1IA,SAAS,iBAAAC,uBAAqB;;;ACb9B,IAAAC,sBAAA;AAAA,SAAAA,qBAAA;AAAA;AAAA;AAAA;AAAA,8BAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAS,KAAAC,UAAS;AAClB,SAAS,4BAA4B;;;ACArC,SAAS,iBAAAC,sBAAqB;AAIvB,IAAM,mBAAN,cAA+B,iBAAwC;AAAA,EAL9E,OAK8E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BtE,YAAY,OAAuC,CAAC,GAAG;AAC7D,UAAM;AAAA,MACL,MAAMC,eAAc;AAAA,MACpB,GAAG;AAAA,MACH,OAAO,KAAK,QAAQ,EAAE,KAAK,KAAK,MAAM,IAAI,IAAI;AAAA,IAC/C,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,aAAqB;AAC1C,SAAK,KAAK,cAAcC,sBAAqB,MAAM,WAAW;AAC9D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,mBAAmB;AACzB,SAAK,KAAK,cAAc;AACxB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,UAAU,MAAM;AACjC,SAAK,KAAK,UAAU,iBAAiB,MAAM,OAAO;AAClD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,OAAO,KAAa;AAC1B,SAAK,KAAK,QAAQ,2BAA2B,MAAM,EAAE,IAAI,CAAC;AAC1D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKgB,SAAgC;AAC/C,+BAA2B,MAAM,KAAK,KAAK,KAAK;AAEhD,WAAO,EAAE,GAAG,KAAK,KAAK;AAAA,EACvB;AACD;;;AD1EO,IAAM,6BAA6BC,GACxC,OAAO;AAAA,EACP,KAAKA,GACH,OAAO,EACP;AAAA,IACA,EAAE,kBAAkB,CAAC,SAAS,UAAU,aAAa,EAAE;AAAA,IACvD,EAAE,SAAS,wEAAwE;AAAA,EACpF;AACF,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,IAAMC,wBAAuBD,GAClC,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,IAAK,EAC3B,qBAAqB,mBAAmB;AAEnC,IAAM,gBAAgBA,GAC3B,OAAO;AAAA,EACP,KAAKA,GACH,OAAO,EACP,IAAI,EAAE,kBAAkB,CAAC,aAAa,EAAE,GAAG,EAAE,SAAS,qDAAqD,CAAC;AAC/G,CAAC,EACA,qBAAqB,mBAAmB;AAEnC,IAAM,mBAAmBA,GAAE,QAAQ;AAEnC,IAAM,mBAAmBA,GAAE,QAAQ;AAEnC,IAAM,mBAAmBA,GAAE,WAAW,oBAAoB;AAE1D,IAAM,8BAA8BA,GACzC,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,GAAK,EAC3B,qBAAqB,mBAAmB;AAEnC,IAAM,qBAAqBA,GAChC,SAAS,aAAa,EACtB,GAAGA,GAAE,SAAS,gBAAgB,CAAC,EAC/B,qBAAqB,mBAAmB;AAEnC,IAAM,0BAA0B,eAAe,QAAQ;AAEvD,SAAS,sBACf,OACA,oBAC8B;AAC9B,EAAAA,GAAE,SAAS,kBAAkB,EAAE,qBAAqB,mBAAmB,EAAE,MAAM,KAAK;AACrF;AALgB;AAOT,SAAS,uBAEd,OAAgB,KAAa,KAAa,oBAA0E;AACrH,GAAC,qBAAqBA,GAAE,SAAS,kBAAkB,IAAIA,GAAE,SAAS,gBAAgB,GAChF,MAAM,EACN,yBAAyB,GAAG,EAC5B,sBAAsB,GAAG,EACzB,qBAAqB,mBAAmB,EACxC,MAAM,KAAK;AACd;AATgB;;;AE9DhB,SAAS,iBAAAE,sBAA4C;AAI9C,IAAM,cAAN,cAA0B,iBAAmC;AAAA,EAJpE,OAIoE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0B5D,YAAY,OAAkC,CAAC,GAAG;AACxD,UAAM,EAAE,MAAMC,eAAc,MAAM,GAAG,MAAM,MAAM,KAAK,OAAO,EAAE,KAAK,KAAK,KAAK,IAAI,IAAI,OAAU,CAAC;AAAA,EAClG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,UAAU,MAAM;AACjC,SAAK,KAAK,UAAU,iBAAiB,MAAM,OAAO;AAClD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,OAAO,KAAa;AAC1B,SAAK,KAAK,OAAO,cAAc,MAAM,EAAE,IAAI,CAAC;AAC5C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKgB,SAA2B;AAC1C,kBAAc,MAAM,KAAK,KAAK,IAAI;AAElC,WAAO,EAAE,GAAG,KAAK,MAAM,MAAM,EAAE,GAAG,KAAK,KAAK,KAAK,EAAE;AAAA,EACpD;AACD;;;AC7DA,SAAS,iBAAAC,uBAAqB;AAIvB,IAAM,mBAAN,cAA+B,iBAAwC;AAAA,EAL9E,OAK8E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBtE,YAAY,OAAuC,CAAC,GAAG;AAC7D,UAAM;AAAA,MACL,MAAMC,gBAAc;AAAA,MACpB,GAAG;AAAA,IACJ,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,UAAU,MAAM;AACjC,SAAK,KAAK,UAAU,iBAAiB,MAAM,OAAO;AAClD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,SAA+B;AAChD,SAAK,KAAK,UAAU,iBAAiB,MAAM,OAAO;AAClD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,eAAe;AACrB,SAAK,KAAK,UAAU;AACpB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKgB,SAAgC;AAC/C,WAAO,EAAE,GAAG,KAAK,KAAK;AAAA,EACvB;AACD;;;ACnEA,SAAS,iBAAAC,uBAAqB;AAIvB,IAAM,qBAAN,cAAiC,iBAA0C;AAAA,EALlF,OAKkF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqB1E,YAAY,OAAyC,CAAC,GAAG;AAC/D,UAAM;AAAA,MACL,MAAMC,gBAAc;AAAA,MACpB,GAAG;AAAA,IACJ,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,SAAiB;AAClC,SAAK,KAAK,UAAU,4BAA4B,MAAM,OAAO;AAC7D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKgB,SAAkC;AACjD,gCAA4B,MAAM,KAAK,KAAK,OAAO;AAEnD,WAAO,EAAE,GAAG,KAAK,KAAK;AAAA,EACvB;AACD;;;ALXO,IAAM,mBAAN,cAA+B,iBAAwC;AAAA,EAxC9E,OAwC8E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCT,YAAY,EAAE,YAAY,GAAG,KAAK,IAAoC,CAAC,GAAG;AAChF,UAAM,EAAE,MAAMC,gBAAc,WAAW,GAAG,KAAK,CAAC;AAChD,SAAK,aAAc,YAAY,IAAI,CAAC,cAAc,uBAAuB,SAAS,CAAC,KAClF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,OAAiC;AAEtD,4BAAwB,MAAM,KAAK;AAEnC,QAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,CAAC,KAAK,OAAO,IAAI,IAAI;AAC3B,WAAK,KAAK,gBAAgB,OAAO,OAAO,SAAS,KAAK;AACtD,aAAO;AAAA,IACR;AAEA,SAAK,KAAK,eAAe;AACzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,mBAAmB;AACzB,SAAK,KAAK,eAAe;AACzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,0BACH,YAKF;AACD,SAAK,WAAW;AAAA,MACf,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,cAAc,eAAe,WAAW,gBAA+B,CAAC;AAAA,IAC5G;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,qBACH,YACF;AACD,SAAK,WAAW,KAAK,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,cAAc,eAAe,WAAW,WAAW,CAAC,CAAC;AAC7G,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,6BACH,YAGF;AACD,SAAK,WAAW;AAAA,MACf,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,cAAc,eAAe,WAAW,mBAAmB,CAAC;AAAA,IAChG;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,wBACH,YACF;AACD,SAAK,WAAW,KAAK,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,cAAc,eAAe,WAAW,cAAc,CAAC,CAAC;AAChH,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,0BACH,YAGF;AACD,SAAK,WAAW,KAAK,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,cAAc,eAAe,WAAW,gBAAgB,CAAC,CAAC;AAClH,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,4BACH,YAGF;AACD,SAAK,WAAW;AAAA,MACf,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,cAAc,eAAe,WAAW,kBAAkB,CAAC;AAAA,IAC/F;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,iBACN,OACA,gBACG,YACF;AACD,SAAK,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA,GAAG,eAAe,UAAU,EAAE;AAAA,QAAI,CAAC,cAClC,qBAAqB,mBAAmB,YAAY,uBAAuB,SAAS;AAAA,MACrF;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,UAAU,MAAM;AACjC,SAAK,KAAK,UAAU,iBAAiB,MAAM,OAAO;AAClD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAAgC;AACtC,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,MACR,YAAY,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,OAAO,CAAC;AAAA,IAClE;AAAA,EACD;AACD;;;AM3OA,SAAS,iBAAAC,uBAAqB;;;ACCvB,IAAM,0BAAN,MAA4E;AAAA,EAJnF,OAImF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIlE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BT,YAAY,OAAqC,CAAC,GAAG;AAC3D,SAAK,OAAO;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,aAAqB;AAC1C,SAAK,KAAK,cAAcC,sBAAqB,MAAM,WAAW;AAC9D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,mBAAmB;AACzB,SAAK,KAAK,cAAc;AACxB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAW,UAAU,MAAM;AACjC,SAAK,KAAK,UAAU,iBAAiB,MAAM,OAAO;AAClD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,OAAO,KAAa;AAC1B,SAAK,KAAK,QAAQ,2BAA2B,MAAM,EAAE,IAAI,CAAC;AAC1D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,SAA8B;AACpC,+BAA2B,MAAM,KAAK,KAAK,KAAK;AAEhD,WAAO,EAAE,GAAG,KAAK,KAAK;AAAA,EACvB;AACD;;;AD5EO,IAAM,sBAAN,cAAkC,iBAA2C;AAAA,EAbpF,OAaoF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAInE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoCT,YAAY,EAAE,OAAO,GAAG,KAAK,IAAuC,CAAC,GAAG;AAC9E,UAAM,EAAE,MAAMC,gBAAc,cAAc,GAAG,KAAK,CAAC;AACnD,SAAK,QAAQ,OAAO,IAAI,CAAC,SAAS,IAAI,wBAAwB,IAAI,CAAC,KAAK,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,YACH,OAGF;AACD,SAAK,MAAM;AAAA,MACV,GAAG,eAAe,KAAK,EAAE,IAAI,CAAC,UAAU;AACvC,cAAM,SAAS,eAAe,OAAO,uBAAuB;AAE5D,8BAAsB,QAAQ,uBAAuB;AACrD,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,YACN,OACA,gBACG,OAGF;AACD,SAAK,MAAM;AAAA,MACV;AAAA,MACA;AAAA,MACA,GAAG,eAAe,KAAK,EAAE,IAAI,CAAC,UAAU;AACvC,cAAM,SAAS,eAAe,OAAO,uBAAuB;AAE5D,8BAAsB,QAAQ,uBAAuB;AACrD,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAAmC;AACzC,2BAAuB,KAAK,OAAO,GAAG,IAAI,uBAAuB;AACjE,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,MACR,OAAO,KAAK,MAAM,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC;AAAA,IAC9C;AAAA,EACD;AACD;;;AE5GA,SAAS,iBAAAC,uBAAqB;AAWvB,IAAM,iBAAN,cAA6B,iBAAsC;AAAA,EAnB1E,OAmB0E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIzD;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqCT,YAAY,EAAE,YAAY,WAAW,GAAG,KAAK,IAAkC,CAAC,GAAG;AACzF,UAAM,EAAE,MAAMC,gBAAc,SAAS,GAAG,KAAK,CAAC;AAC9C,SAAK,aAAc,YAAY,IAAI,CAAC,cAAc,uBAAuB,SAAS,CAAC,KAAK,CAAC;AACzF,SAAK,YAAY,YAAY,uBAAuB,SAAS,IAAI;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,mBACN,WACO;AACP,YAAQ,IAAI,MAAM,aAAa,mBAAmB,MAAM,eAAe,WAAW,aAAa,CAAC,CAAC;AACjG,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,sBACN,WACO;AACP,YAAQ,IAAI,MAAM,aAAa,mBAAmB,MAAM,eAAe,WAAW,gBAAgB,CAAC,CAAC;AACpG,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,4BACH,YACF;AACD,SAAK,WAAW;AAAA,MACf,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,UAAU;AAC5C,cAAM,SAAS,eAAe,OAAO,kBAAkB;AAEvD,8BAAsB,QAAQ,kBAAkB;AAChD,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,4BACN,OACA,gBACG,YAGF;AACD,SAAK,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,UAAU;AAC5C,cAAM,SAAS,eAAe,OAAO,kBAAkB;AAEvD,8BAAsB,QAAQ,kBAAkB;AAChD,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAA8B;AACpC,2BAAuB,KAAK,YAAY,GAAG,GAAG,kBAAkB;AAChE,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,MACR,YAAY,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,OAAO,CAAC;AAAA,MACjE,WAAW,mBAAmB,MAAM,KAAK,SAAS,EAAE,OAAO;AAAA,IAC5D;AAAA,EACD;AACD;;;AlB3BO,SAAS,uBACf,MACmB;AACnB,MAAI,gBAAgB,kBAAkB;AACrC,WAAO;AAAA,EACR;AAEA,UAAQ,KAAK,MAAM;AAAA,IAClB,KAAKC,gBAAc;AAClB,aAAO,IAAI,iBAAiB,IAAI;AAAA,IACjC,KAAKA,gBAAc;AAClB,aAAO,IAAI,cAAc,IAAI;AAAA,IAC9B,KAAKA,gBAAc;AAClB,aAAO,IAAI,wBAAwB,IAAI;AAAA,IACxC,KAAKA,gBAAc;AAClB,aAAO,IAAI,iBAAiB,IAAI;AAAA,IACjC,KAAKA,gBAAc;AAClB,aAAO,IAAI,sBAAsB,IAAI;AAAA,IACtC,KAAKA,gBAAc;AAClB,aAAO,IAAI,sBAAsB,IAAI;AAAA,IACtC,KAAKA,gBAAc;AAClB,aAAO,IAAI,6BAA6B,IAAI;AAAA,IAC7C,KAAKA,gBAAc;AAClB,aAAO,IAAI,yBAAyB,IAAI;AAAA,IACzC,KAAKA,gBAAc;AAClB,aAAO,IAAI,YAAY,IAAI;AAAA,IAC5B,KAAKA,gBAAc;AAClB,aAAO,IAAI,iBAAiB,IAAI;AAAA,IACjC,KAAKA,gBAAc;AAClB,aAAO,IAAI,eAAe,IAAI;AAAA,IAC/B,KAAKA,gBAAc;AAClB,aAAO,IAAI,iBAAiB,IAAI;AAAA,IACjC,KAAKA,gBAAc;AAClB,aAAO,IAAI,mBAAmB,IAAI;AAAA,IACnC,KAAKA,gBAAc;AAClB,aAAO,IAAI,iBAAiB,IAAI;AAAA,IACjC,KAAKA,gBAAc;AAClB,aAAO,IAAI,oBAAoB,IAAI;AAAA,IACpC;AAEC,YAAM,IAAI,MAAM,6CAA6C,KAAK,IAAI,EAAE;AAAA,EAC1E;AACD;AA1CgB;AA4ChB,SAAS,UACR,SACA,aACqB;AACrB,SAAO,mBAAmB;AAC3B;AALS;AAOF,SAAS,eACf,SACA,aACC;AACD,MAAI,UAAU,SAAS,WAAW,GAAG;AACpC,WAAO;AAAA,EACR;AAEA,MAAI,OAAO,YAAY,YAAY;AAClC,WAAO,QAAQ,IAAI,YAAY,CAAC;AAAA,EACjC;AAEA,SAAO,IAAI,YAAY,OAAO;AAC/B;AAbgB;;;AF7HT,IAAM,mBAAN,cAA0E,iBAE/E;AAAA,EArDF,OAqDE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoCT,YAAY,EAAE,YAAY,GAAG,KAAK,IAA6D,CAAC,GAAG;AACzG,UAAM,EAAE,MAAMC,gBAAc,WAAW,GAAG,KAAK,CAAC;AAChD,SAAK,aAAc,YAAY,IAAI,CAAC,cAAc,uBAAuB,SAAS,CAAC,KAAK,CAAC;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,iBAAiB,YAAwC;AAC/D,SAAK,WAAW,KAAK,GAAG,eAAe,UAAU,CAAC;AAClD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,iBAAiB,YAAwC;AAC/D,SAAK,WAAW,OAAO,GAAG,KAAK,WAAW,QAAQ,GAAG,eAAe,UAAU,CAAC;AAC/E,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAAqE;AAC3E,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,MACR,YAAY,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,OAAO,CAAC;AAAA,IAClE;AAAA,EACD;AACD;;;AqB/HA,IAAAC,sBAAA;AAAA,SAAAA,qBAAA;AAAA;AAAA;AAAA,oCAAAC;AAAA;AAAA,SAAS,KAAAC,UAAS;AAKX,IAAM,iBAAiBC,GAC5B,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,qBAAqB,mBAAmB;AACnC,IAAM,sBAAsBA,GACjC,SAAS,gBAAgB,EACzB,MAAM,EACN,yBAAyB,CAAC,EAC1B,qBAAqB,mBAAmB;AAEnC,SAASC,4BACf,UACA,OACA,YACC;AACD,oBAAkB,MAAM,QAAQ;AAChC,iBAAe,MAAM,KAAK;AAC1B,sBAAoB,MAAM,UAAU;AACrC;AARgB,OAAAA,6BAAA;;;ACCT,IAAM,eAAN,MAAqF;AAAA,EAjB5F,OAiB4F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI3E;AAAA;AAAA;AAAA;AAAA,EAKA,aAAiE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO3E,YAAY,EAAE,YAAY,GAAG,KAAK,IAAsD,CAAC,GAAG;AAClG,SAAK,OAAO,EAAE,GAAG,KAAK;AACtB,SAAK,aAAc,YAAY,IAAI,CAAC,cAAc,uBAAuB,SAAS,CAAC,KAClF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAS,OAAe;AAC9B,SAAK,KAAK,QAAQ,eAAe,MAAM,KAAK;AAC5C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,YAAY,UAAkB;AACpC,SAAK,KAAK,YAAY,kBAAkB,MAAM,QAAQ;AACtD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,iBACH,YAGF;AACD,SAAK,WAAW;AAAA,MACf,GAAG,eAAe,UAAU,EAAE;AAAA,QAAI,CAAC,cAClC,qBAAqB,mBAClB,YACA,IAAI,iBAAiD,SAAS;AAAA,MAClE;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,iBAAiB,YAA2E;AAClG,SAAK,WAAW,OAAO,GAAG,KAAK,WAAW,QAAQ,GAAG,eAAe,UAAU,CAAC;AAC/E,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAAkD;AACxD,IAAAC,4BAA2B,KAAK,KAAK,WAAW,KAAK,KAAK,OAAO,KAAK,UAAU;AAEhF,WAAO;AAAA,MACN,GAAG,KAAK;AAAA,MACR,YAAY,KAAK,WAAW,IAAI,CAAC,cAAc,UAAU,OAAO,CAAC;AAAA,IAClE;AAAA,EACD;AACD;;;ACpGA,IAAAC,sBAAA;AAAA,SAAAA,qBAAA;AAAA,+BAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAAAC;AAAA;AAAA,SAAS,KAAAC,UAAS;AAClB;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,OAGM;AAMP,IAAM,gBAAgBC,GACpB,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,MAAM,6DAA6D,EACnE,qBAAqB,mBAAmB;AAEnC,SAAS,aAAa,MAAuC;AACnE,gBAAc,MAAM,IAAI;AACzB;AAFgB;AAIhB,IAAMC,wBAAuBD,GAC3B,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,GAAG,EACzB,qBAAqB,mBAAmB;AAC1C,IAAM,kBAAkBA,GAAE,WAAW,MAAM;AAEpC,SAAS,oBAAoB,aAAqD;AACxF,EAAAC,sBAAqB,MAAM,WAAW;AACvC;AAFgB;AAIhB,IAAM,0BAA0BD,GAAE,QAAQ,EAAE,MAAM,EAAE,sBAAsB,EAAE,EAAE,qBAAqB,mBAAmB;AAC/G,SAAS,eAAe,QAAiB;AAC/C,SAAO,gBAAgB,MAAM,MAAM;AACpC;AAFgB;AAIT,SAAS,yBAAyB,SAAuE;AAC/G,0BAAwB,MAAM,OAAO;AACtC;AAFgB;AAIT,SAASE,4BACf,MACA,aACA,SACC;AAED,eAAa,IAAI;AAGjB,sBAAoB,WAAW;AAG/B,2BAAyB,OAAO;AACjC;AAbgB,OAAAA,6BAAA;AAehB,IAAM,mBAAmBF,GAAE,QAAQ;AAE5B,SAAS,0BAA0B,OAA0C;AACnF,mBAAiB,MAAM,KAAK;AAC7B;AAFgB;AAIT,SAAS,iBAAiB,UAAgD;AAChF,mBAAiB,MAAM,QAAQ;AAChC;AAFgB;AAIhB,IAAM,yBAAyBA,GAAE,OAAO,EAAE,gBAAgB,EAAE,EAAE,qBAAqB,mBAAmB;AAE/F,SAAS,sBAAsB,cAAsB,SAAqD;AAChH,yBAAuB,OAAO,SAAS,UAAU,KAAK,YAAY;AACnE;AAFgB;AAIT,SAASG,uBAEd,OAAgB,oBAAuE;AACxF,EAAAH,GAAE,SAAS,kBAAkB,EAAE,MAAM,KAAK;AAC3C;AAJgB,OAAAG,wBAAA;AAMT,IAAM,2BAA2BH,GACtC,OAAwB,OAAO,YAAY,OAAO,OAAO,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQA,GAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EACjH,OAAO,EACP,QAAQ,EACR,qBAAqB,mBAAmB;AAEnC,SAAS,wBAAwB,OAAkD;AACzF,2BAAyB,MAAM,KAAK;AACrC;AAFgB;AAIhB,IAAM,wBAAwBA,GAAE,QAAQ,EAAE,QAAQ;AAE3C,SAAS,qBAAqB,OAA6D;AACjG,wBAAsB,MAAM,KAAK;AAClC;AAFgB;AAIhB,IAAM,4BAA4BA,GAChC,MAAM;AAAA,EACNA,GAAE,OAAO,EAAE,UAAU,CAAC,UAAU,MAAM,SAAS,CAAC;AAAA,EAChDA,GACE,OAAO,EACP,QAAQ,EACR,UAAU,CAAC,UAAU,MAAM,SAAS,CAAC;AAAA,EACvCA,GAAE,OAAO,EAAE,MAAM,OAAO;AACzB,CAAC,EACA,QAAQ;AAEH,SAAS,iCAAiC,aAAsB;AACtE,SAAO,0BAA0B,MAAM,WAAW;AACnD;AAFgB;AAIT,SAAS,aAAa,OAA0C;AACtE,mBAAiB,MAAM,KAAK;AAC7B;AAFgB;AAIT,IAAM,oBAAoBA,GAAE;AAAA,EAClCA,GAAE,WAAW,sBAAsB,EAAE,qBAAqB,mBAAmB;AAC9E;AAEO,IAAM,4BAA4BA,GAAE;AAAA,EAC1CA,GAAE,WAAW,0BAA0B,EAAE,qBAAqB,mBAAmB;AAClF;;;ACnHA,SAAS,OAAAI,YAAW;;;ACDb,IAAM,2BAAN,MAA+B;AAAA,EANtC,OAMsC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIrB;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,QAAQ,MAAoB;AAElC,iBAAa,IAAI;AAEjB,YAAQ,IAAI,MAAM,QAAQ,IAAI;AAE9B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,eAAe,aAAqB;AAE1C,wBAAoB,WAAW;AAE/B,YAAQ,IAAI,MAAM,eAAe,WAAW;AAE5C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,oBAAoB,QAAsB,eAA8B;AAC9E,QAAI,CAAC,KAAK,oBAAoB;AAC7B,cAAQ,IAAI,MAAM,sBAAsB,CAAC,CAAC;AAAA,IAC3C;AAEA,UAAM,eAAe,eAAe,MAAM;AAE1C,QAAI,kBAAkB,MAAM;AAC3B,WAAK,mBAAoB,YAAY,IAAI;AACzC,aAAO;AAAA,IACR;AAEA,iBAAa,aAAa;AAE1B,SAAK,mBAAoB,YAAY,IAAI;AACzC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,qBAAqB,gBAAwC;AACnE,QAAI,mBAAmB,MAAM;AAC5B,cAAQ,IAAI,MAAM,sBAAsB,IAAI;AAC5C,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,MAAM,sBAAsB,CAAC,CAAC;AAE1C,eAAW,QAAQ,OAAO,QAAQ,cAAc,GAAG;AAClD,WAAK,oBAAoB,GAAI,IAAsC;AAAA,IACpE;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,2BAA2B,QAAsB,sBAAqC;AAC5F,QAAI,CAAC,KAAK,2BAA2B;AACpC,cAAQ,IAAI,MAAM,6BAA6B,CAAC,CAAC;AAAA,IAClD;AAEA,UAAM,eAAe,eAAe,MAAM;AAE1C,QAAI,yBAAyB,MAAM;AAClC,WAAK,0BAA2B,YAAY,IAAI;AAChD,aAAO;AAAA,IACR;AAEA,wBAAoB,oBAAoB;AAExC,SAAK,0BAA2B,YAAY,IAAI;AAChD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,4BAA4B,uBAA+C;AACjF,QAAI,0BAA0B,MAAM;AACnC,cAAQ,IAAI,MAAM,6BAA6B,IAAI;AACnD,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,MAAM,6BAA6B,CAAC,CAAC;AACjD,eAAW,QAAQ,OAAO,QAAQ,qBAAqB,GAAG;AACzD,WAAK,2BAA2B,GAAI,IAAsC;AAAA,IAC3E;AAEA,WAAO;AAAA,EACR;AACD;;;AC7IA;AAAA,EACC;AAAA,OAMM;AAkBA,IAAM,qBAAN,MAAyB;AAAA,EAzBhC,OAyBgC;AAAA;AAAA;AAAA,EACf,OAAe;AAAA,EAEf;AAAA,EAEA,cAAsB;AAAA,EAEtB;AAAA,EAEA,UAA4C,CAAC;AAAA,EAE7C;AAAA;AAAA;AAAA;AAAA,EAKA,qBAA0C;AAAA,EAE1C,6BAA6D;AAAA;AAAA;AAAA;AAAA,EAK7D,gBAAqC;AAAA,EAErC;AAAA,EAEA,OAA4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrC,eAAe,UAA+C;AACpE,YAAQ,IAAI,MAAM,YAAY,kBAAkB,MAAM,eAAe,QAAQ,CAAC,CAAC;AAE/E,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,uBAAuB,kBAA2D;AACxF,YAAQ,IAAI,MAAM,qBAAqB,0BAA0B,MAAM,eAAe,gBAAgB,CAAC,CAAC;AAExG,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,qBAAqB,OAAgB;AAE3C,8BAA0B,KAAK;AAE/B,YAAQ,IAAI,MAAM,sBAAsB,KAAK;AAE7C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,4BAA4B,aAA+D;AAEjG,UAAM,kBAAkB,iCAAiC,WAAW;AAEpE,YAAQ,IAAI,MAAM,8BAA8B,eAAe;AAE/D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYO,gBAAgB,SAAqC;AAE3D,yBAAqB,OAAO;AAE5B,YAAQ,IAAI,MAAM,iBAAiB,OAAO;AAE1C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,QAAQ,OAAO,MAAM;AAE3B,iBAAa,IAAI;AACjB,YAAQ,IAAI,MAAM,QAAQ,IAAI;AAC9B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,SAA0D;AAChE,IAAAC,4BAA2B,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO;AAEpE,4BAAwB,KAAK,kBAAkB;AAC/C,4BAAwB,KAAK,yBAAyB;AAEtD,WAAO;AAAA,MACN,GAAG;AAAA,MACH,MAAM,uBAAuB;AAAA,MAC7B,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,CAAC;AAAA,IACtD;AAAA,EACD;AACD;;;ACjKA,SAAS,oCAAgF;;;ACOlF,IAAe,+BAAf,cAAoD,yBAAyB;AAAA,EAPpF,OAOoF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWnE,WAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7B,YAAY,UAAmB;AAErC,qBAAiB,QAAQ;AAEzB,YAAQ,IAAI,MAAM,YAAY,QAAQ;AAEtC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAcU,yBAAyB;AAClC,IAAAC,4BAA2B,KAAK,MAAM,KAAK,aAAa,CAAC,CAAC;AAG1D,4BAAwB,KAAK,kBAAkB;AAC/C,4BAAwB,KAAK,yBAAyB;AAGtD,qBAAiB,KAAK,QAAQ;AAAA,EAC/B;AACD;;;ADlDO,IAAM,+BAAN,cAA2C,6BAA6B;AAAA,EAN/E,OAM+E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIrD,OAAO,6BAA6B;AAAA;AAAA;AAAA;AAAA,EAKtD,SAAgD;AACtD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;;;AEpBA,SAAS,gCAAAC,qCAA6E;AAM/E,IAAM,4BAAN,cAAwC,6BAA6B;AAAA,EAN5E,OAM4E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI3D,OAAOC,8BAA6B;AAAA;AAAA;AAAA;AAAA,EAK7C,SAA6C;AACnD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;;;ACpBA,SAAS,gCAAAC,qCAA6E;AACtF,SAAS,WAAW;;;ACDpB,SAAS,KAAAC,UAAS;AAClB,SAAS,eAAAC,oBAAmB;AAS5B,IAAM,sBAAsB;AAAA,EAC3BC,aAAY;AAAA,EACZA,aAAY;AAAA,EACZA,aAAY;AAAA,EACZA,aAAY;AAAA,EACZA,aAAY;AAAA,EACZA,aAAY;AAAA,EACZA,aAAY;AAAA,EACZA,aAAY;AAAA,EACZA,aAAY;AAAA,EACZA,aAAY;AACb;AAOA,IAAM,wBAAwBC,GAAE,MAAMA,GAAE,MAAM,oBAAoB,IAAI,CAAC,SAASA,GAAE,QAAQ,IAAI,CAAC,CAAC,CAAC;AAK1F,IAAM,4CAAN,MAAgD;AAAA,EAjCvD,OAiCuD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAItC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,mBAAmB,cAAwE;AACjG,QAAI,KAAK,kBAAkB,QAAW;AACrC,cAAQ,IAAI,MAAM,iBAAiB,CAAC,CAAC;AAAA,IACtC;AAEA,SAAK,cAAe,KAAK,GAAG,sBAAsB,MAAM,eAAe,YAAY,CAAC,CAAC;AAErF,WAAO;AAAA,EACR;AACD;;;AD5CO,IAAM,4BAAN,cAAwC,6BAA6B;AAAA;AAAA;AAAA;AAAA,EAIlD,OAAOC,8BAA6B;AAAA;AAAA;AAAA;AAAA,EAKtD,SAA6C;AACnD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AAd4E;AAA/D,4BAAN;AAAA,EADN,IAAI,yCAAyC;AAAA,GACjC;;;AETb,SAAS,KAAAC,WAAS;AAClB,SAAS,gCAAAC,qCAA6E;AACtF,SAAS,OAAAC,YAAW;;;ACCb,IAAe,kDAAf,MAA+D;AAAA,EAHtE,OAGsE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIrD;AAAA;AAAA;AAAA;AAAA,EAKA;AAejB;;;AC3BA,SAAS,KAAAC,UAAS;AAGlB,IAAMC,oBAAmBC,GAAE,QAAQ;AAK5B,IAAM,gDAAN,MAAoD;AAAA,EAR3D,OAQ2D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,gBAAgB,cAA6B;AAEnD,IAAAD,kBAAiB,MAAM,YAAY;AAEnC,QAAI,gBAAgB,aAAa,QAAQ,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,QAAQ,SAAS,GAAG;AAChG,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,YAAQ,IAAI,MAAM,gBAAgB,YAAY;AAE9C,WAAO;AAAA,EACR;AACD;;;ACtCA,SAAS,KAAAE,UAAS;AAClB,SAAS,gCAAAC,qCAA4E;AAIrF,IAAM,kBAAkBC,GAAE,OAAO,EAAE,yBAAyB,CAAC,EAAE,sBAAsB,GAAG;AACxF,IAAM,kBAAkBA,GAAE,OAAO,EAAE,YAAY,OAAO,iBAAiB,EAAE,SAAS,OAAO,iBAAiB;AAC1G,IAAM,mBAAmBA,GACvB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,oBAAoB;AAAA,EACpB,OAAOA,GAAE,MAAM,CAAC,iBAAiB,eAAe,CAAC;AAClD,CAAC,EACA,MAAM;AAKD,IAAM,2CAAN,MAAmF;AAAA,EAlB1F,OAkB0F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,cAAc,SAA2E;AAC/F,UAAM,oBAAoB,eAAe,OAAO;AAChD,QAAI,kBAAkB,SAAS,KAAK,kBAAkB,QAAQ,KAAK,cAAc;AAChF,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,qBAAiB,MAAM,iBAAiB;AAExC,QAAI,KAAK,YAAY,QAAW;AAC/B,cAAQ,IAAI,MAAM,WAAW,CAAC,CAAC;AAAA,IAChC;AAEA,0BAAsB,kBAAkB,QAAQ,KAAK,OAAO;AAE5D,eAAW,EAAE,MAAM,oBAAoB,MAAM,KAAK,mBAAmB;AAEpE,UAAI,KAAK,SAASC,8BAA6B,QAAQ;AACtD,wBAAgB,MAAM,KAAK;AAAA,MAC5B,OAAO;AACN,wBAAgB,MAAM,KAAK;AAAA,MAC5B;AAEA,WAAK,QAAS,KAAK,EAAE,MAAM,oBAAoB,MAAM,CAAC;AAAA,IACvD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAA2E,SAAmC;AACpH,UAAM,oBAAoB,eAAe,OAAO;AAChD,QAAI,kBAAkB,SAAS,KAAK,kBAAkB,QAAQ,KAAK,cAAc;AAChF,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,qBAAiB,MAAM,iBAAiB;AAExC,YAAQ,IAAI,MAAM,WAAW,CAAC,CAAC;AAC/B,SAAK,WAAW,iBAAiB;AAEjC,WAAO;AAAA,EACR;AACD;;;AH1EA,IAAM,kBAAkBC,IAAE,OAAO,EAAE,IAAI;AAUhC,IAAM,4BAAN,cACE,6BAET;AAAA;AAAA;AAAA;AAAA,EAIiB,OAAOC,8BAA6B;AAAA;AAAA;AAAA;AAAA,EAK7C,YAAY,KAAmB;AACrC,oBAAgB,MAAM,GAAG;AAEzB,YAAQ,IAAI,MAAM,aAAa,GAAG;AAElC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,YAAY,KAAmB;AACrC,oBAAgB,MAAM,GAAG;AAEzB,YAAQ,IAAI,MAAM,aAAa,GAAG;AAElC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAA6C;AACnD,SAAK,uBAAuB;AAE5B,QAAI,KAAK,gBAAgB,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,QAAQ,SAAS,GAAG;AAChF,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AAxCA;AAHa,4BAAN;AAAA,EALNC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,GACa;;;AIlBb,SAAS,gCAAAC,qCAAiF;AAMnF,IAAM,gCAAN,cAA4C,6BAA6B;AAAA,EANhF,OAMgF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI/D,OAAOC,8BAA6B;AAAA;AAAA;AAAA;AAAA,EAK7C,SAAiD;AACvD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;;;ACpBA,SAAS,KAAAC,WAAS;AAClB,SAAS,gCAAAC,qCAA4E;AACrF,SAAS,OAAAC,YAAW;AAMpB,IAAMC,mBAAkBC,IAAE,OAAO;AAU1B,IAAM,2BAAN,cACE,6BAET;AAAA;AAAA;AAAA;AAAA,EAIiB,OAAOC,8BAA6B;AAAA;AAAA;AAAA;AAAA,EAK7C,YAAY,KAAmB;AACrC,IAAAF,iBAAgB,MAAM,GAAG;AAEzB,YAAQ,IAAI,MAAM,aAAa,GAAG;AAElC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,YAAY,KAAmB;AACrC,IAAAA,iBAAgB,MAAM,GAAG;AAEzB,YAAQ,IAAI,MAAM,aAAa,GAAG;AAElC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAA4C;AAClD,SAAK,uBAAuB;AAE5B,QAAI,KAAK,gBAAgB,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,QAAQ,SAAS,GAAG;AAChF,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AAxCA;AAHa,2BAAN;AAAA,EALNG;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,GACa;;;AClBb,SAAS,gCAAAC,qCAA0E;AAM5E,IAAM,yBAAN,cAAqC,6BAA6B;AAAA,EANzE,OAMyE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAI/C,OAAOC,8BAA6B;AAAA;AAAA;AAAA;AAAA,EAKtD,SAA0C;AAChD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;;;ACpBA,SAAS,KAAAC,WAAS;AAClB,SAAS,gCAAAC,qCAA4E;AACrF,SAAS,OAAAC,YAAW;AAKpB,IAAMC,sBAAqBC,IAAE,OAAO,EAAE,mBAAmB,CAAC,EAAE,gBAAgB,GAAK;AACjF,IAAMC,sBAAqBD,IAAE,OAAO,EAAE,mBAAmB,CAAC,EAAE,gBAAgB,GAAK;AAM1E,IAAM,2BAAN,cAAuC,6BAA6B;AAAA;AAAA;AAAA;AAAA,EAI1D,OAAOE,8BAA6B;AAAA;AAAA;AAAA;AAAA,EAKpC;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,aAAa,KAAmB;AACtC,IAAAD,oBAAmB,MAAM,GAAG;AAE5B,YAAQ,IAAI,MAAM,cAAc,GAAG;AAEnC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,aAAa,KAAmB;AACtC,IAAAF,oBAAmB,MAAM,GAAG;AAE5B,YAAQ,IAAI,MAAM,cAAc,GAAG;AAEnC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKO,SAA4C;AAClD,SAAK,uBAAuB;AAE5B,QAAI,KAAK,gBAAgB,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,QAAQ,SAAS,GAAG;AAChF,YAAM,IAAI,WAAW,gEAAgE;AAAA,IACtF;AAEA,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;AAtD2E;AAA9D,2BAAN;AAAA,EADNI,KAAI,+CAA+C,wCAAwC;AAAA,GAC/E;;;ACdb,SAAS,gCAAAC,sCAA0E;AAM5E,IAAM,yBAAN,cAAqC,6BAA6B;AAAA,EANzE,OAMyE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIxD,OAAOC,+BAA6B;AAAA;AAAA;AAAA;AAAA,EAK7C,SAA0C;AAChD,SAAK,uBAAuB;AAE5B,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;;;ACFO,IAAM,4BAAN,MAEL;AAAA,EApBF,OAoBE;AAAA;AAAA;AAAA,EACe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,iBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,yBAAyB;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,OAA+F;AACnH,WAAO,KAAK,uBAAuB,OAAO,sBAAsB;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,iBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,yBAAyB;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,OAA+F;AACnH,WAAO,KAAK,uBAAuB,OAAO,sBAAsB;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,oBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,4BAA4B;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,qBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,6BAA6B;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,gBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,wBAAwB;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,iBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,yBAAyB;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,gBACN,OACC;AACD,WAAO,KAAK,uBAAuB,OAAO,wBAAwB;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,uBACP,OACA,UACyB;AACzB,UAAM,EAAE,QAAQ,IAAI;AAGpB,6BAAyB,OAAO;AAGhC,UAAM,SAAS,OAAO,UAAU,aAAa,MAAM,IAAI,SAAS,CAAC,IAAI;AAErE,IAAAC,uBAAsB,QAAQ,QAAQ;AAGtC,YAAQ,KAAK,MAAM;AAEnB,WAAO;AAAA,EACR;AACD;;;AChJA;AAAA,EACC,gCAAAC;AAAA,OAGM;AACP,SAAS,OAAAC,YAAW;AAab,IAAM,qCAAN,MAAmF;AAAA;AAAA;AAAA;AAAA,EAIzE,OAAe;AAAA;AAAA;AAAA;AAAA,EAKf,cAAsB;AAAA;AAAA;AAAA;AAAA,EAKtB,UAA2C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrD,cACN,OAGC;AACD,UAAM,EAAE,QAAQ,IAAI;AAGpB,6BAAyB,OAAO;AAIhC,UAAM,SAAS,OAAO,UAAU,aAAa,MAAM,IAAI,8BAA8B,CAAC,IAAI;AAG1F,IAAAC,uBAAsB,QAAQ,6BAA6B;AAG3D,YAAQ,KAAK,MAAM;AAEnB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,SAAqD;AAC3D,IAAAC,4BAA2B,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO;AAEpE,WAAO;AAAA,MACN,MAAMC,+BAA6B;AAAA,MACnC,MAAM,KAAK;AAAA,MACX,oBAAoB,KAAK;AAAA,MACzB,aAAa,KAAK;AAAA,MAClB,2BAA2B,KAAK;AAAA,MAChC,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,CAAC;AAAA,IACtD;AAAA,EACD;AACD;AA/D0F;AAA7E,qCAAN;AAAA,EADNC,KAAI,wBAAwB;AAAA,GAChB;AAyEN,IAAM,gCAAN,MAA8E;AAAA;AAAA;AAAA;AAAA,EAIpE,OAAe;AAAA;AAAA;AAAA;AAAA,EAKf,cAAsB;AAAA;AAAA;AAAA;AAAA,EAKtB,UAA0C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASpD,SAAgD;AACtD,IAAAF,4BAA2B,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO;AAEpE,WAAO;AAAA,MACN,MAAMC,+BAA6B;AAAA,MACnC,MAAM,KAAK;AAAA,MACX,oBAAoB,KAAK;AAAA,MACzB,aAAa,KAAK;AAAA,MAClB,2BAA2B,KAAK;AAAA,MAChC,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,CAAC;AAAA,IACtD;AAAA,EACD;AACD;AAnCqF;AAAxE,gCAAN;AAAA,EADNC,KAAI,0BAA0B,yBAAyB;AAAA,GAC3C;;;AClFN,IAAM,gCAAN,MAEL;AAAA,EAXF,OAWE;AAAA;AAAA;AAAA,EACe,UAA4C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtD,mBACN,OAG6B;AAC7B,UAAM,EAAE,QAAQ,IAAI;AAGpB,6BAAyB,OAAO;AAGhC,UAAM,SAAS,OAAO,UAAU,aAAa,MAAM,IAAI,mCAAmC,CAAC,IAAI;AAE/F,IAAAC,uBAAsB,QAAQ,kCAAkC;AAGhE,YAAQ,KAAK,MAAM;AAEnB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cACN,OAG6B;AAC7B,UAAM,EAAE,QAAQ,IAAI;AAGpB,6BAAyB,OAAO;AAGhC,UAAM,SAAS,OAAO,UAAU,aAAa,MAAM,IAAI,8BAA8B,CAAC,IAAI;AAE1F,IAAAA,uBAAsB,QAAQ,6BAA6B;AAG3D,YAAQ,KAAK,MAAM;AAEnB,WAAO;AAAA,EACR;AACD;;;AnBhDO,IAAM,sBAAN,MAA0B;AAAA;AAAA;AAAA;AAAA,EAIhB,OAAe;AAAA;AAAA;AAAA;AAAA,EAKf;AAAA;AAAA;AAAA;AAAA,EAKA,cAAsB;AAAA;AAAA;AAAA;AAAA,EAKtB;AAAA;AAAA;AAAA;AAAA,EAKA,UAA4C,CAAC;AAAA;AAAA;AAAA;AAAA,EAK7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAA0C;AAAA;AAAA;AAAA;AAAA,EAK1C,6BAA6D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU7D,gBAAqC;AAAA;AAAA;AAAA;AAAA,EAKrC;AAAA;AAAA;AAAA;AAAA,EAKA,OAA4B;AAC7C;AA9DiC;AAApB,sBAAN;AAAA,EADNC,KAAI,2BAA2B,0BAA0B,+BAA+B,kBAAkB;AAAA,GAC9F;;;AoBjBb,IAAAC,sBAAA;AAAA,SAAAA,qBAAA;AAAA,2BAAAC;AAAA,EAAA,iCAAAC;AAAA,EAAA,4BAAAC;AAAA,EAAA,wCAAAC;AAAA,EAAA,iCAAAC;AAAA,EAAA,oBAAAC;AAAA,EAAA,kCAAAC;AAAA,EAAA;AAAA;AAAA,SAAS,KAAAC,WAAS;AAClB,SAAS,0BAAAC,yBAAwB,8BAAAC,6BAA4B,0BAAAC,+BAA8B;AAI3F,IAAMC,iBAAgBC,IACpB,OAAO,EACP,yBAAyB,CAAC,EAC1B,sBAAsB,EAAE,EACxB,MAAM,IAAI,EACV,qBAAqB,mBAAmB;AAC1C,IAAM,gBAAgBA,IACpB,MAAM,CAACA,IAAE,QAAQC,wBAAuB,IAAI,GAAGD,IAAE,QAAQC,wBAAuB,OAAO,CAAC,CAAC,EACzF,qBAAqB,mBAAmB;AAC1C,IAAMC,oBAAmBF,IAAE,QAAQ;AAE5B,SAASG,2BAA0B,OAA0C;AACnF,EAAAD,kBAAiB,MAAM,KAAK;AAC7B;AAFgB,OAAAC,4BAAA;AAIT,SAASC,cAAa,MAAuC;AACnE,EAAAL,eAAc,MAAM,IAAI;AACzB;AAFgB,OAAAK,eAAA;AAIT,SAAS,aAAa,MAAuD;AACnF,gBAAc,MAAM,IAAI;AACzB;AAFgB;AAIT,SAASC,4BAA2B,MAAc,MAAc;AAEtE,EAAAD,cAAa,IAAI;AAGjB,eAAa,IAAI;AAClB;AANgB,OAAAC,6BAAA;AAQhB,IAAMC,yBAAwBN,IAAE,QAAQ,EAAE,QAAQ;AAE3C,SAASO,sBAAqB,OAA6D;AACjG,EAAAD,uBAAsB,MAAM,KAAK;AAClC;AAFgB,OAAAC,uBAAA;AAIhB,IAAMC,6BAA4BR,IAChC,MAAM;AAAA,EACNA,IAAE,OAAO,EAAE,UAAU,CAAC,UAAU,MAAM,SAAS,CAAC;AAAA,EAChDA,IACE,OAAO,EACP,QAAQ,EACR,UAAU,CAAC,UAAU,MAAM,SAAS,CAAC;AAAA,EACvCA,IAAE,OAAO,EAAE,MAAM,OAAO;AACzB,CAAC,EACA,QAAQ;AAEH,SAASS,kCAAiC,aAAsB;AACtE,SAAOD,2BAA0B,MAAM,WAAW;AACnD;AAFgB,OAAAC,mCAAA;AAIT,IAAMC,qBAAoBV,IAAE;AAAA,EAClCA,IAAE,WAAWW,uBAAsB,EAAE,qBAAqB,mBAAmB;AAC9E;AAEO,IAAMC,6BAA4BZ,IAAE;AAAA,EAC1CA,IAAE,WAAWa,2BAA0B,EAAE,qBAAqB,mBAAmB;AAClF;;;AChCO,IAAM,4BAAN,MAAgC;AAAA,EA/BvC,OA+BuC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAItB,OAAe;AAAA;AAAA;AAAA;AAAA,EAKf;AAAA;AAAA;AAAA;AAAA,EAKA,OAA+B;AAAA;AAAA;AAAA;AAAA,EAK/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAA0C;AAAA;AAAA;AAAA;AAAA,EAK1C,6BAA6D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU7D,gBAAqC;AAAA;AAAA;AAAA;AAAA,EAKrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,eAAe,UAA+C;AACpE,YAAQ,IAAI,MAAM,YAAYC,mBAAkB,MAAM,eAAe,QAAQ,CAAC,CAAC;AAE/E,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,uBAAuB,kBAA2D;AACxF,YAAQ,IAAI,MAAM,qBAAqBC,2BAA0B,MAAM,eAAe,gBAAgB,CAAC,CAAC;AAExG,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,QAAQ,MAAc;AAE5B,IAAAC,cAAa,IAAI;AAEjB,YAAQ,IAAI,MAAM,QAAQ,IAAI;AAE9B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,QAAQ,MAA8B;AAE5C,iBAAa,IAAI;AAEjB,YAAQ,IAAI,MAAM,QAAQ,IAAI;AAE9B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,qBAAqB,OAAgB;AAE3C,IAAAC,2BAA0B,KAAK;AAE/B,YAAQ,IAAI,MAAM,sBAAsB,KAAK;AAE7C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,4BAA4B,aAA+D;AAEjG,UAAM,kBAAkBC,kCAAiC,WAAW;AAEpE,YAAQ,IAAI,MAAM,8BAA8B,eAAe;AAE/D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,gBAAgB,SAAqC;AAE3D,IAAAC,sBAAqB,OAAO;AAE5B,YAAQ,IAAI,MAAM,iBAAiB,OAAO;AAE1C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,oBAAoB,QAAsB,eAA8B;AAC9E,QAAI,CAAC,KAAK,oBAAoB;AAC7B,cAAQ,IAAI,MAAM,sBAAsB,CAAC,CAAC;AAAA,IAC3C;AAEA,UAAM,eAAe,eAAe,MAAM;AAE1C,QAAI,kBAAkB,MAAM;AAC3B,WAAK,mBAAoB,YAAY,IAAI;AACzC,aAAO;AAAA,IACR;AAEA,IAAAH,cAAa,aAAa;AAE1B,SAAK,mBAAoB,YAAY,IAAI;AACzC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,qBAAqB,gBAAwC;AACnE,QAAI,mBAAmB,MAAM;AAC5B,cAAQ,IAAI,MAAM,sBAAsB,IAAI;AAC5C,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,MAAM,sBAAsB,CAAC,CAAC;AAE1C,eAAW,QAAQ,OAAO,QAAQ,cAAc;AAC/C,WAAK,oBAAoB,GAAI,IAAsC;AACpE,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,SAA4D;AAClE,IAAAI,4BAA2B,KAAK,MAAM,KAAK,IAAI;AAE/C,4BAAwB,KAAK,kBAAkB;AAE/C,WAAO,EAAE,GAAG,KAAK;AAAA,EAClB;AACD;;;ACvOO,SAAS,YAAY,MAAgB;AAC3C,UACE,KAAK,OAAO,UAAU,MACtB,KAAK,aAAa,UAAU,MAC5B,KAAK,QAAQ,OAAO,CAAC,MAAM,SAAS,OAAO,KAAK,KAAK,SAAS,KAAK,MAAM,QAAQ,CAAC,KAAK,MACvF,KAAK,QAAQ,KAAK,UAAU,MAC5B,KAAK,QAAQ,KAAK,UAAU;AAE/B;AARgB;;;AjD0ET,IAAM,UAAU;", "names": ["Assertions_exports", "s", "s", "ComponentType", "ComponentType", "ComponentType", "ComponentType", "ComponentType", "SelectMenuDefaultValueType", "ComponentType", "SelectMenuDefaultValueType", "ComponentType", "SelectMenuDefaultValueType", "ComponentType", "SelectMenuDefaultValueType", "ComponentType", "ComponentType", "ComponentType", "SelectMenuDefaultValueType", "ComponentType", "SelectMenuDefaultValueType", "ComponentType", "Assertions_exports", "placeholder<PERSON><PERSON><PERSON><PERSON>", "s", "s", "placeholder<PERSON><PERSON><PERSON><PERSON>", "ComponentType", "placeholder<PERSON><PERSON><PERSON><PERSON>", "ComponentType", "Assertions_exports", "descriptionPredicate", "s", "ComponentType", "ComponentType", "descriptionPredicate", "s", "descriptionPredicate", "ComponentType", "ComponentType", "ComponentType", "ComponentType", "ComponentType", "ComponentType", "ComponentType", "ComponentType", "descriptionPredicate", "ComponentType", "ComponentType", "ComponentType", "ComponentType", "ComponentType", "Assertions_exports", "validateRequiredParameters", "s", "s", "validateRequiredParameters", "validateRequiredParameters", "Assertions_exports", "assertReturnOfBuilder", "validateRequiredParameters", "s", "s", "descriptionPredicate", "validateRequiredParameters", "assertReturnOfBuilder", "mix", "validateRequiredParameters", "validateRequiredParameters", "ApplicationCommandOptionType", "ApplicationCommandOptionType", "ApplicationCommandOptionType", "s", "ChannelType", "ChannelType", "s", "ApplicationCommandOptionType", "s", "ApplicationCommandOptionType", "mix", "s", "booleanPredicate", "s", "s", "ApplicationCommandOptionType", "s", "ApplicationCommandOptionType", "s", "ApplicationCommandOptionType", "mix", "ApplicationCommandOptionType", "ApplicationCommandOptionType", "s", "ApplicationCommandOptionType", "mix", "numberValidator", "s", "ApplicationCommandOptionType", "mix", "ApplicationCommandOptionType", "ApplicationCommandOptionType", "s", "ApplicationCommandOptionType", "mix", "minLengthValidator", "s", "maxLengthValidator", "ApplicationCommandOptionType", "mix", "ApplicationCommandOptionType", "ApplicationCommandOptionType", "assertReturnOfBuilder", "ApplicationCommandOptionType", "mix", "assertReturnOfBuilder", "validateRequiredParameters", "ApplicationCommandOptionType", "mix", "assertReturnOfBuilder", "mix", "Assertions_exports", "contextsPredicate", "integrationTypesPredicate", "validateDMPermission", "validateDefaultMemberPermissions", "validateDefaultPermission", "validateName", "validateRequiredParameters", "s", "ApplicationCommandType", "ApplicationIntegrationType", "InteractionContextType", "namePredicate", "s", "ApplicationCommandType", "booleanPredicate", "validateDefaultPermission", "validateName", "validateRequiredParameters", "dmPermissionPredicate", "validateDMPermission", "memberPermissionPredicate", "validateDefaultMemberPermissions", "contextsPredicate", "InteractionContextType", "integrationTypesPredicate", "ApplicationIntegrationType", "contextsPredicate", "integrationTypesPredicate", "validateName", "validateDefaultPermission", "validateDefaultMemberPermissions", "validateDMPermission", "validateRequiredParameters"]}