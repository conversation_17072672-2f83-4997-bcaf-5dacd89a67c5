{"SERVER_ENABLED": true, "SERVER_SHORTNAME": "StonedFlorida420 2x", "SERVER_SPECIAL_ID": "server3", "SERVER_IP": "2x.stonedflorida420.com", "SERVER_PORT": 28041, "RCON_PORT": 28050, "RCON_PASS": "Rizzo9676", "BOT_TOKEN": "MTM4MzQ0MDYxNTM0OTU1NTI5MQ.G4A8iK.MoaXyxpdnSYbKb6W8FJYyl_0c1Kp67jb4sTP4w", "BOT_CLIENT_ID": "1223591210715844758", "USING UR PLUS PLUGIN": true, "LEADERBOARD": {"ENABLED": false, "CHANNEL_ID": "", "DEFAULT_DISPLAY(wipe, lifetime)": "lifetime", "COLOR(original, green, blue, black, yellow, orange, pink, red, purple, custom)": "custom", "CUSTOM_COLOR": "#242424"}, "SERVER_STATUS_PAGE": {"ENABLED": true, "CHANNEL_ID": "1363973923666006058", "COLOR(original, green, blue, black, yellow, orange, pink, red, purple, custom)": "black", "CUSTOM_COLOR": "#000000"}, "WIPE_ANNOUNCEMENTS": {"ENABLED": true, "WEBHOOK": "https://discord.com/api/webhooks/1419834323028279486/yGEJ1-VzgPmE1XPdcmGIfJMJSs_QF-PySEOEsaJFFOm-8Wc1PAc6p2Ix80yw6EtDnOFB", "EMBED_SETTINGS": {"EXTERNAL_CONTENT": "", "TITLE": "{StonedFlorida420 2x} has wiped!", "DESCRIPTION": "**CONNECTION INFO:**\nConnect: ``{*************}:{28041}``\nQuick Connect: steam://connect/{*************}:{28041}\n\n**LINKS:**\n**Store:** https://store.stonedflorida.com//\n**Linking:** /\n**Steam:** ", "SMALL_IMAGE": "https://imgur.com/0JLG9JX.png", "LARGE_IMAGE": {"INFO": "If you want this to be a picture of the map, you will need to provide your rustmaps API key and it will override the large image", "DISCLAIMER": "If the map is not generated on RustMaps it will use your large image as a fall back, if there is no large image there will be no large image on the embed.", "RUSTMAPS_API_KEY": "6277019bd15d468ab3801921dbe0dbd8", "LARGE_IMAGE": "https://imgur.com/0JLG9JX.png"}, "FOOTER": "Just Wiped", "EMBED_COLOR": "#4fff87"}}, "CHAT_LOGS": {"DO_YOU_USE_BETTER_CHAT": true, "SIMPLE_FORMATTING": true, "GLOBAL_CHAT_LOGS": {"ENABLED": true, "GLOBAL_CHAT_WEBHOOK": "https://discord.com/api/webhooks/1419834641472557208/7IPN5W5eo-EP7a2Wn2xYGRdK8icUiXrFG_3yMqawI5XAx7FKflJlIo_M9GMXju6hNP7X", "EMBED_COLOR": "#00ff26"}, "TEAM_CHAT_LOGS": {"ENABLED": false, "TEAM_CHAT_WEBHOOK": "", "EMBED_COLOR": "#ff0008"}, "LOCAL_CHAT_LOGS": {"ENABLED": false, "LOCAL_CHAT_WEBHOOK": "", "EMBED_COLOR": "#fffb7d"}, "DISCORD_TO_INGAME_MESSAGES": {"ENABLED": true, "CHAT_CHANNEL_IDS": ["1419774240403488941"], "REQUIRE_ROLES_TO_SEND_MESSAGES": true, "REQUIRED_ROLES": ["admin"], "MESSAGE_FORMAT": "<color=#7289DA>[DISCORD] {user}:</color>"}}, "USER_MUTING": {"INFO": "All the stuff below will require you to have Better <PERSON><PERSON> Mu<PERSON> from UMod on your server", "AUTOMATED_MUTING": {"ENABLED": true, "WATCH_TEAM_CHAT": false, "MUTE_WORDS_AND_REASONS": [{"BLOCKED_WORDS": {"EXACT_WORD_MATCHES": ["fag0t", "faggot", "bitch", "fuck", "cunt", "nigger", "nigga"], "PARTIAL_WORD_MATCHES": ["fagg", "nig"]}, "MUTE_TIME (s/m/h/d)": "24h", "MUTE_REASON": "You'll have that on them bigger jobs"}, {"BLOCKED_WORDS": {"EXACT_WORD_MATCHES": ["nigger", "fag0t", "faggot", "bitch", "fuck", "cunt"], "PARTIAL_WORD_MATCHES": ["nigge"]}, "MUTE_TIME (s/m/h/d)": "5m", "MUTE_REASON": "It be likt that sometimes"}], "LOG_AUTO_MUTES": false, "SIMPLE_FORMATTING": true, "LOG_WEBHOOK": "", "EMBED_COLOR": "#ff0008"}}, "DYNAMIC_MAXPLAYERS_CHANGER": {"ENABLED": false, "OPTIONS": {"DONT_CHANGE_POP_IF_FPS_IS_LESS_THAN": "15", "BASIC": {"ENABLED": false, "CONDITIONALS": [{"POP_IS_GREATER_THAN": "0", "INCREASE_MAX_PLAYERS_TO": "25"}, {"POP_IS_GREATER_THAN": "25", "INCREASE_MAX_PLAYERS_TO": "50"}, {"POP_IS_GREATER_THAN": "50", "INCREASE_MAX_PLAYERS_TO": "100"}]}, "QUEUEING": {"ENABLED": false, "NOTE": "The sections below will only change the max pop if the queue is equal to or greater then the set number.", "QUEUE_COUNT_TO_INCREASE": "10", "CONDITIONALS": [{"POP_IS_GREATER_THAN": "140", "INCREASE_MAX_PLAYERS_TO": "175"}, {"POP_IS_GREATER_THAN": "165", "INCREASE_MAX_PLAYERS_TO": "200"}]}}, "LOGGING": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#49e637"}}, "RCON_SETTINGS": {"RCON_MESSAGE_LOGS": {"ENABLED": true, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#00ff26", "DONT_SEND_RCON_MESSAGES_THAT_INCLUDE": ["NullReferenceException", "no valid dismount", "[Better Chat]", "\"Channel\":0", "\"Channel\":1"], "MESSAGE_CHUNKING_COUNT": 5}, "RCON_COMMANDS": {"ENABLED": true, "COMMAND_PREFIX": "$", "STAFF_ROLES": ["ROLE_ID", "ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"], "CUSTOM_COMMANDS": {"info": "Set the command name to the first work in the command. If you want to grant permission for the kick command set the name to kick", "COMMANDS": [{"COMMAND_NAME": "kick", "REQUIRE_ROLE": false, "COMMAND_ROLE_PERMS": ["ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"]}, {"COMMAND_NAME": "ban", "REQUIRE_ROLE": true, "COMMAND_ROLE_PERMS": ["ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"]}, {"COMMAND_NAME": "mute", "REQUIRE_ROLE": true, "COMMAND_ROLE_PERMS": ["ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"]}]}}}, "SERVER_ONLINE_OFFLINE": {"ENABLED": true, "SIMPLE_FORMATTING": false, "ONLINE_EMBED_SETTINGS": {"WEBHOOK": "https://discord.com/api/webhooks/1419835711879905321/KSKNE1aH29J1reyfmVzsSoKa8frFHqGw9GHwadobaSMYtLzeR5YceGOyvGKnicmnT1s7", "TITLE": "{StonedFlorida420 2x} has gone online!", "DESCRIPTION": "Join That Shit! *************:28041", "LARGE_IMAGE": "https://imgur.com/0JLG9JX.png", "SMALL_IMAGE": "", "FOOTER": "SERVER ONLINE", "COLOR": "#49e637"}, "OFFLINE_EMBED_SETTINGS": {"WEBHOOK": "https://discord.com/api/webhooks/1419835711879905321/KSKNE1aH29J1reyfmVzsSoKa8frFHqGw9GHwadobaSMYtLzeR5YceGOyvGKnicmnT1s7", "TITLE": "{StonedFlorida420 2x} has gone offline!", "DESCRIPTION": "Please allow it time to boot up! Sorry for any inconveniences", "LARGE_IMAGE": "https://imgur.com/0JLG9JX.png", "SMALL_IMAGE": "", "FOOTER": "SERVER OFFLINE", "COLOR": "#eb4034"}}, "USE_POP_AS_A_BOT_STATUS": {"ENABLED": true, "OPTIONS": {"SERVER_OFFLINE_MESSAGE": "[ OFFLINE ]", "DIDNT_WIPE_TODAY": {"PLAYER_COUNT_MESSAGE": "({playersOnline}/{maxPlayers}) Online!..", "PLAYERS_JOINING_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({joiningPlayers} Joining!)", "PLAYERS_QUEUED_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({queuedPlayers} Queued!)", "ENABLE_THRESHOLD_MESSAGE": false, "THRESHOLD_PERCENT": "20", "THRESHOLD_MESSAGE": "come join!"}, "WIPED_TODAY": {"ENABLED": true, "MAX_HOURS_SINCE_LAST_WIPE": "24", "WIPED_TODAY_STATUS": {"PLAYER_COUNT_MESSAGE": "({playersOnline}/{maxPlayers}) Wiped Today!", "PLAYERS_JOINING_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({joiningPlayers} Joining!)", "PLAYERS_QUEUED_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({queuedPlayers} Queued!)", "ENABLE_THRESHOLD_MESSAGE": false, "THRESHOLD_PERCENT": "20", "THRESHOLD_MESSAGE": "Come join! Wiped today!"}}}}, "PLAYER_ACCOUNT_CHECKS": {"BAN_CHECKER": {"INFO": "This will check the player when they connect to the server if they have had a temp ban from rust", "ENABLED": true, "SIMPLE_FORMATTING": true, "THRESHOLDS": {"RUST_TEMP_BANS": 1, "VAC_BANS": 1, "EAC_BANS": 1, "DAYS_SINCE_LAST_BAN": 30}, "MENTION_STAFF_ROLES": [""], "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}}, "SERVER_LOGGING": {"F7_REPORT_LOGGING": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "F1_SPAWN_ITEM_LOGS": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "SERVER_JOIN_LOGS": {"ENABLED": true, "USE_PLUGIN_JOIN_LOGS": false, "SIMPLE_FORMATTING": true, "LOG_WEBHOOK": "https://discord.com/api/webhooks/1419837357930053723/PMIl9XVvCZ_nxa2xzDk-6TK9FBdhwLEWXS6LIHe-AcvKX7en12TZSKp62ueHgCITLCJH", "EMBED_COLOR": "#03dffc"}, "SERVER_LEAVE_LOGS": {"ENABLED": true, "USE_PLUGIN_LEAVE_LOGS": false, "SIMPLE_FORMATTING": true, "LOG_WEBHOOK": "https://discord.com/api/webhooks/1419837357930053723/PMIl9XVvCZ_nxa2xzDk-6TK9FBdhwLEWXS6LIHe-AcvKX7en12TZSKp62ueHgCITLCJH", "EMBED_COLOR": "#03dffc"}, "(SERVER)MESSAGE_LOGS": {"ENABLED": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "KILL_LOGS": {"ENABLED": false, "SIMPLE_FORMATTING": true, "USE_PLUGIN_KILL_LOGS": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "PRIVATE_MESSAGES": {"INFO": "THIS REQUIRES THE PRIVATE MESSAGES PLUGIN FROM UMOD", "ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}}, "PLUGIN_ONLY_LOGGING": {"EVENT_SPAWNS": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "PERM_CHANGES": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "FEEDBACK_LOGGING": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}}, "BOT_VERSION": {"info": "It's recommend to not edit the version unless you know what you're doing", "version": "v3.1.0"}}