{"SERVER_ENABLED": true, "SERVER_SHORTNAME": "StonedFlorida420 10x", "SERVER_SPECIAL_ID": "server1", "SERVER_IP": "10x.stonedflorida420.com", "SERVER_PORT": 28015, "RCON_PORT": 28016, "RCON_PASS": "Rizzo9676", "BOT_TOKEN": "MTM1MzEyMzAxMzI5NzcwNTA2MA.Goapbd.qok7RRK_hyybbsJ0Ukq87vNaue_lYpE6lsX4zo", "BOT_CLIENT_ID": "1233897994168832091", "USING UR PLUS PLUGIN": true, "LEADERBOARD": {"ENABLED": false, "CHANNEL_ID": "", "DEFAULT_DISPLAY(wipe, lifetime)": "lifetime", "COLOR(original, green, blue, black, yellow, orange, pink, red, purple, custom)": "custom", "CUSTOM_COLOR": "#242424"}, "SERVER_STATUS_PAGE": {"ENABLED": true, "CHANNEL_ID": "1419810411536060616", "COLOR(original, green, blue, black, yellow, orange, pink, red, purple, custom)": "black", "CUSTOM_COLOR": "#000000"}, "WIPE_ANNOUNCEMENTS": {"ENABLED": true, "WEBHOOK": "https://discord.com/api/webhooks/1419828376260186195/JamHLPQ3QjC_ZEtGzn_UDhCqDbU81mx5Bu9lx5QNy1OpLEA_lSFnpzwXoC-JmCaCJXG-", "EMBED_SETTINGS": {"EXTERNAL_CONTENT": "", "TITLE": "{StonedFlorida420 10X} has wiped!", "DESCRIPTION": "**CONNECTION INFO:**\nConnect: ``{*************}:{28015}``\nQuick Connect: steam://connect/{*************}:{28015}\n\n**LINKS:**\n**Store:** https://stonedflorida420.com/\n**Linking:** https://stonedflorida420.com/\n**Steam:** ", "SMALL_IMAGE": "", "LARGE_IMAGE": {"INFO": "If you want this to be a picture of the map, you will need to provide your rustmaps API key and it will override the large image", "DISCLAIMER": "If the map is not generated on RustMaps it will use your large image as a fall back, if there is no large image there will be no large image on the embed.", "RUSTMAPS_API_KEY": "6277019bd15d468ab3801921dbe0dbd8", "LARGE_IMAGE": "https://imgur.com/0JLG9JX.png"}, "FOOTER": "Just Wiped", "EMBED_COLOR": "#4fff87"}}, "CHAT_LOGS": {"DO_YOU_USE_BETTER_CHAT": true, "SIMPLE_FORMATTING": true, "GLOBAL_CHAT_LOGS": {"ENABLED": true, "GLOBAL_CHAT_WEBHOOK": "https://discord.com/api/webhooks/1419831449166680184/u34wnGtm7vyRSrFOlRNDWTlp0uNtFQwzv1m0L2VYzDCdGD5v79jJQN74o79iF_IiArJB", "EMBED_COLOR": "#00ff26"}, "TEAM_CHAT_LOGS": {"ENABLED": false, "TEAM_CHAT_WEBHOOK": "", "EMBED_COLOR": "#ff0008"}, "LOCAL_CHAT_LOGS": {"ENABLED": false, "LOCAL_CHAT_WEBHOOK": "", "EMBED_COLOR": "#fffb7d"}, "DISCORD_TO_INGAME_MESSAGES": {"ENABLED": true, "CHAT_CHANNEL_IDS": ["1419810352950153387"], "REQUIRE_ROLES_TO_SEND_MESSAGES": false, "REQUIRED_ROLES": [""], "MESSAGE_FORMAT": "<color=#7289DA>[DISCORD] {user}:</color>"}}, "USER_MUTING": {"INFO": "All the stuff below will require you to have Better <PERSON><PERSON> Mu<PERSON> from UMod on your server", "AUTOMATED_MUTING": {"ENABLED": true, "WATCH_TEAM_CHAT": false, "MUTE_WORDS_AND_REASONS": [{"BLOCKED_WORDS": {"EXACT_WORD_MATCHES": ["fag0t", "faggot", "bitch", "ass", "fuck", "shit", "cunt"], "PARTIAL_WORD_MATCHES": ["fagg", "nig"]}, "MUTE_TIME (s/m/h/d)": "5m", "MUTE_REASON": "It Be Like That Sometimes"}, {"BLOCKED_WORDS": {"EXACT_WORD_MATCHES": ["nig", "nigger", "fag0t", "faggot", "bitch", "ass", "fuck", "shit", "cunt"], "PARTIAL_WORD_MATCHES": ["nigge"]}, "MUTE_TIME (s/m/h/d)": "5m", "MUTE_REASON": "It Be Like That Sometimes"}], "LOG_AUTO_MUTES": false, "SIMPLE_FORMATTING": true, "LOG_WEBHOOK": "", "EMBED_COLOR": "#ff0008"}}, "DYNAMIC_MAXPLAYERS_CHANGER": {"ENABLED": false, "OPTIONS": {"DONT_CHANGE_POP_IF_FPS_IS_LESS_THAN": "15", "BASIC": {"ENABLED": false, "CONDITIONALS": [{"POP_IS_GREATER_THAN": "0", "INCREASE_MAX_PLAYERS_TO": "25"}, {"POP_IS_GREATER_THAN": "25", "INCREASE_MAX_PLAYERS_TO": "50"}, {"POP_IS_GREATER_THAN": "50", "INCREASE_MAX_PLAYERS_TO": "100"}]}, "QUEUEING": {"ENABLED": false, "NOTE": "The sections below will only change the max pop if the queue is equal to or greater then the set number.", "QUEUE_COUNT_TO_INCREASE": "10", "CONDITIONALS": [{"POP_IS_GREATER_THAN": "140", "INCREASE_MAX_PLAYERS_TO": "175"}, {"POP_IS_GREATER_THAN": "165", "INCREASE_MAX_PLAYERS_TO": "200"}]}}, "LOGGING": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#49e637"}}, "RCON_SETTINGS": {"RCON_MESSAGE_LOGS": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#00ff26", "DONT_SEND_RCON_MESSAGES_THAT_INCLUDE": ["NullReferenceException", "no valid dismount", "[Better Chat]", "\"Channel\":0", "\"Channel\":1"], "MESSAGE_CHUNKING_COUNT": 5}, "RCON_COMMANDS": {"ENABLED": false, "COMMAND_PREFIX": "$", "STAFF_ROLES": ["ROLE_ID", "ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"], "CUSTOM_COMMANDS": {"info": "Set the command name to the first work in the command. If you want to grant permission for the kick command set the name to kick", "COMMANDS": [{"COMMAND_NAME": "kick", "REQUIRE_ROLE": false, "COMMAND_ROLE_PERMS": ["ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"]}, {"COMMAND_NAME": "ban", "REQUIRE_ROLE": true, "COMMAND_ROLE_PERMS": ["ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"]}, {"COMMAND_NAME": "mute", "REQUIRE_ROLE": true, "COMMAND_ROLE_PERMS": ["ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"]}]}}}, "SERVER_ONLINE_OFFLINE": {"ENABLED": true, "SIMPLE_FORMATTING": false, "ONLINE_EMBED_SETTINGS": {"WEBHOOK": "https://discord.com/api/webhooks/1419833139299094699/AKxe3pLcmlSgVppwQ96QK__LuDS1FNz-zckofGQwDjb_nLiB7XDNGf6wgUf1Nb7-cJzx", "TITLE": "{StonedFlorida420 10X} has gone online!", "DESCRIPTION": "Join That Shit! *************:28015", "LARGE_IMAGE": "https://imgur.com/0JLG9JX.png", "SMALL_IMAGE": "", "FOOTER": "SERVER ONLINE", "COLOR": "#49e637"}, "OFFLINE_EMBED_SETTINGS": {"WEBHOOK": "https://discord.com/api/webhooks/1419833139299094699/AKxe3pLcmlSgVppwQ96QK__LuDS1FNz-zckofGQwDjb_nLiB7XDNGf6wgUf1Nb7-cJzx", "TITLE": "{StonedFlorida420 10X} has gone offline!", "DESCRIPTION": "Please allow it time to boot up! Sorry for any inconveniences", "LARGE_IMAGE": "https://imgur.com/0JLG9JX.png", "SMALL_IMAGE": "", "FOOTER": "SERVER OFFLINE", "COLOR": "#eb4034"}}, "USE_POP_AS_A_BOT_STATUS": {"ENABLED": true, "OPTIONS": {"SERVER_OFFLINE_MESSAGE": "[ OFFLINE ]", "DIDNT_WIPE_TODAY": {"PLAYER_COUNT_MESSAGE": "({playersOnline}/{maxPlayers}) Online!..", "PLAYERS_JOINING_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({joiningPlayers} Joining!)", "PLAYERS_QUEUED_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({queuedPlayers} Queued!)", "ENABLE_THRESHOLD_MESSAGE": false, "THRESHOLD_PERCENT": "20", "THRESHOLD_MESSAGE": "come join!"}, "WIPED_TODAY": {"ENABLED": true, "MAX_HOURS_SINCE_LAST_WIPE": "24", "WIPED_TODAY_STATUS": {"PLAYER_COUNT_MESSAGE": "({playersOnline}/{maxPlayers}) Wiped Today!", "PLAYERS_JOINING_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({joiningPlayers} Joining!)", "PLAYERS_QUEUED_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({queuedPlayers} Queued!)", "ENABLE_THRESHOLD_MESSAGE": false, "THRESHOLD_PERCENT": "20", "THRESHOLD_MESSAGE": "Come join! Wiped today!"}}}}, "PLAYER_ACCOUNT_CHECKS": {"BAN_CHECKER": {"INFO": "This will check the player when they connect to the server if they have had a temp ban from rust", "ENABLED": true, "SIMPLE_FORMATTING": true, "THRESHOLDS": {"RUST_TEMP_BANS": 1, "VAC_BANS": 1, "EAC_BANS": 1, "DAYS_SINCE_LAST_BAN": 30}, "MENTION_STAFF_ROLES": ["ROLE_ID", "ROLE_ID"], "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}}, "SERVER_LOGGING": {"F7_REPORT_LOGGING": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "F1_SPAWN_ITEM_LOGS": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "SERVER_JOIN_LOGS": {"ENABLED": true, "USE_PLUGIN_JOIN_LOGS": true, "SIMPLE_FORMATTING": true, "LOG_WEBHOOK": "https://discord.com/api/webhooks/1419833709321916486/DYet-c6TKFjW38KZvbdZd40hDyGz6Vmax7BhtcM3GwQdv8D45bxOAqKTKWOvym3Jtx-5", "EMBED_COLOR": "#03dffc"}, "SERVER_LEAVE_LOGS": {"ENABLED": true, "USE_PLUGIN_LEAVE_LOGS": true, "SIMPLE_FORMATTING": true, "LOG_WEBHOOK": "https://discord.com/api/webhooks/1419833709321916486/DYet-c6TKFjW38KZvbdZd40hDyGz6Vmax7BhtcM3GwQdv8D45bxOAqKTKWOvym3Jtx-5", "EMBED_COLOR": "#03dffc"}, "(SERVER)MESSAGE_LOGS": {"ENABLED": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "KILL_LOGS": {"ENABLED": false, "SIMPLE_FORMATTING": true, "USE_PLUGIN_KILL_LOGS": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "PRIVATE_MESSAGES": {"INFO": "THIS REQUIRES THE PRIVATE MESSAGES PLUGIN FROM UMOD", "ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}}, "PLUGIN_ONLY_LOGGING": {"EVENT_SPAWNS": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "PERM_CHANGES": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "FEEDBACK_LOGGING": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}}, "BOT_VERSION": {"info": "It's recommend to not edit the version unless you know what you're doing", "version": "v3.0.6"}}