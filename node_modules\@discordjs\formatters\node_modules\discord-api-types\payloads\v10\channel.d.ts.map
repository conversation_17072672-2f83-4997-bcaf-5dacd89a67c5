{"version": 3, "file": "channel.d.ts", "sourceRoot": "", "sources": ["channel.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAChE,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AAC9C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AAEtC,MAAM,WAAW,qBAAqB;IACrC;;OAEG;IACH,EAAE,EAAE,SAAS,CAAC;IACd;;;;OAIG;IACH,IAAI,EAAE,WAAW,CAAC;CAClB;AAED,MAAM,WAAW,kBAAkB;IAClC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,iBAAkB,SAAQ,qBAAqB,EAAE,kBAAkB;CAAG;AAEvF;;GAEG;AACH,MAAM,WAAW,gBAAiB,SAAQ,QAAQ,CAAC,iBAAiB,CAAC;IACpE;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B;;;;OAIG;IACH,UAAU,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,GAAG,SAAS,CAAC;CACrD;AAED;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAE3G;;;GAGG;AACH,MAAM,WAAW,cAAc,CAAC,CAAC,SAAS,WAAW,CAAE,SAAQ,qBAAqB;IACnF,IAAI,EAAE,CAAC,CAAC;IACR,KAAK,CAAC,EAAE,YAAY,CAAC;CACrB;AAED,MAAM,MAAM,eAAe,GACxB,WAAW,CAAC,kBAAkB,GAC9B,WAAW,CAAC,EAAE,GACd,WAAW,CAAC,OAAO,GACnB,WAAW,CAAC,iBAAiB,GAC7B,WAAW,CAAC,eAAe,GAC3B,WAAW,CAAC,SAAS,GACrB,WAAW,CAAC,UAAU,GACtB,WAAW,CAAC,aAAa,GACzB,WAAW,CAAC,YAAY,CAAC;AAE5B,MAAM,MAAM,gBAAgB,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;AAE1F,MAAM,WAAW,kBAAkB,CAAC,CAAC,SAAS,WAAW,CAAE,SAAQ,cAAc,CAAC,CAAC,CAAC;IACnF;;;;;;;;OAQG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;CAC7B;AAED,MAAM,WAAW,kBAAkB;IAClC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,mBAAmB,CAAC,CAAC,SAAS,WAAW,CAAE,SAAQ,cAAc,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;IAC3G;;OAEG;IACH,eAAe,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC;CACnC;AAED,MAAM,WAAW,aAAa,CAAC,CAAC,SAAS,WAAW,CAAE,SAAQ,cAAc,CAAC,CAAC,CAAC;IAC9E;;;OAGG;IACH,kBAAkB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACnC;AAED,MAAM,WAAW,eAAe,CAAC,CAAC,SAAS,WAAW,CAAE,SAAQ,cAAc,CAAC,CAAC,CAAC;IAChF;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,YAAY,EAAE,CAAC;IACvC;;;;;;OAMG;IACH,SAAS,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC;IAC7B;;OAEG;IACH,IAAI,CAAC,EAAE,OAAO,CAAC;CACf;AAED,MAAM,MAAM,oBAAoB,GAAG,OAAO,CAAC,eAAe,EAAE,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;AAElG,MAAM,WAAW,mBAAmB,CAAC,CAAC,SAAS,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,GAAG,oBAAoB,CACpH,SAAQ,mBAAmB,CAAC,CAAC,CAAC,EAC7B,eAAe,CAAC,CAAC,CAAC,EAClB,kBAAkB,EAClB,aAAa,CAAC,CAAC,CAAC;IACjB;;OAEG;IACH,6BAA6B,CAAC,EAAE,yBAAyB,CAAC;IAC1D;;;OAGG;IACH,kCAAkC,CAAC,EAAE,MAAM,CAAC;IAC5C;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACtB;AAED,MAAM,MAAM,cAAc,GAAG,mBAAmB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACxE,MAAM,MAAM,cAAc,GAAG,mBAAmB,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;AAChF,MAAM,WAAW,uBAAwB,SAAQ,eAAe,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,kBAAkB;CAAG;AAElH,MAAM,WAAW,mBAAmB,CAAC,CAAC,SAAS,WAAW,CACzD,SAAQ,eAAe,CAAC,CAAC,CAAC,EACzB,kBAAkB,EAClB,mBAAmB,CAAC,CAAC,CAAC,EACtB,kBAAkB,CAAC,CAAC,CAAC;IACtB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,gBAAgB,CAAC;CACtC;AAED,MAAM,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAE/E,MAAM,MAAM,yBAAyB,GAAG,mBAAmB,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AAEzF,MAAM,WAAW,gBAAgB,CAAC,CAAC,SAAS,WAAW,CAAE,SAAQ,mBAAmB,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;IACxG;;;;OAIG;IACH,UAAU,CAAC,EAAE,OAAO,EAAE,CAAC;CACvB;AAED,MAAM,WAAW,YAAa,SAAQ,gBAAgB,CAAC,WAAW,CAAC,EAAE,CAAC;IACrE;;OAEG;IACH,IAAI,EAAE,IAAI,CAAC;CACX;AAED,MAAM,WAAW,iBAAkB,SAAQ,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC;IAC/E;;OAEG;IACH,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB;;OAEG;IACH,cAAc,CAAC,EAAE,SAAS,CAAC;IAC3B;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB;;OAEG;IACH,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB;;OAEG;IACH,eAAe,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC;IACnC;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,MAAM,MAAM,iBAAiB,GAAG,WAAW,CAAC,kBAAkB,GAAG,WAAW,CAAC,aAAa,GAAG,WAAW,CAAC,YAAY,CAAC;AAEtH,MAAM,WAAW,gBAAgB,CAAC,IAAI,SAAS,iBAAiB,GAAG,iBAAiB,CACnF,SAAQ,mBAAmB,CAAC,IAAI,CAAC,EAChC,eAAe,CAAC,IAAI,CAAC,EACrB,aAAa,CAAC,IAAI,CAAC;IACpB;;OAEG;IACH,MAAM,CAAC,EAAE,eAAe,CAAC;IACzB;;OAEG;IACH,eAAe,CAAC,EAAE,iBAAiB,CAAC;IACpC;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B;;OAEG;IACH,YAAY,EAAE,SAAS,EAAE,CAAC;CAC1B;AAED,MAAM,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AAChF,MAAM,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAClF,MAAM,MAAM,4BAA4B,GAAG,gBAAgB,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAE5F;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAChC;;OAEG;IACH,EAAE,EAAE,SAAS,CAAC;IACd;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,SAAS,EAAE,OAAO,CAAC;IACnB;;OAEG;IACH,QAAQ,EAAE,SAAS,GAAG,IAAI,CAAC;IAC3B;;OAEG;IACH,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,WAAW,iCAAiC;IACjD;;OAEG;IACH,QAAQ,EAAE,SAAS,GAAG,IAAI,CAAC;IAC3B;;OAEG;IACH,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;CAC1B;AAED;;GAEG;AACH,oBAAY,aAAa;IACxB;;OAEG;IACH,cAAc,IAAA;IACd;;OAEG;IACH,YAAY,IAAA;CACZ;AAED;;GAEG;AACH,oBAAY,eAAe;IAC1B;;OAEG;IACH,MAAM,IAAA;IACN;;OAEG;IACH,QAAQ,IAAA;IACR;;OAEG;IACH,WAAW,IAAA;CACX;AAED,MAAM,WAAW,oBAAoB,CAAC,CAAC,SAAS,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAC9F,SAAQ,eAAe,CAAC,CAAC,CAAC,EACzB,kBAAkB;IACnB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB;;OAEG;IACH,eAAe,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC;IACnC;;;;;OAKG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B;;;OAGG;IACH,kBAAkB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACnC;;OAEG;IACH,6BAA6B,CAAC,EAAE,yBAAyB,CAAC;IAC1D;;OAEG;IACH,cAAc,EAAE,gBAAgB,EAAE,CAAC;IACnC;;;OAGG;IACH,kCAAkC,CAAC,EAAE,MAAM,CAAC;IAC5C;;OAEG;IACH,sBAAsB,EAAE,iCAAiC,GAAG,IAAI,CAAC;IACjE;;OAEG;IACH,kBAAkB,EAAE,aAAa,GAAG,IAAI,CAAC;CACzC;AAED,MAAM,WAAW,oBAAqB,SAAQ,oBAAoB,CAAC,WAAW,CAAC,UAAU,CAAC;IACzF;;;;OAIG;IACH,oBAAoB,EAAE,eAAe,CAAC;CACtC;AAED,MAAM,MAAM,oBAAoB,GAAG,oBAAoB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAEhF;;GAEG;AACH,MAAM,MAAM,UAAU,GACnB,4BAA4B,GAC5B,YAAY,GACZ,iBAAiB,GACjB,uBAAuB,GACvB,oBAAoB,GACpB,oBAAoB,GACpB,yBAAyB,GACzB,oBAAoB,GACpB,cAAc,GACd,uBAAuB,GACvB,sBAAsB,GACtB,cAAc,CAAC;AAElB;;GAEG;AACH,oBAAY,WAAW;IACtB;;OAEG;IACH,SAAS,IAAA;IACT;;OAEG;IACH,EAAE,IAAA;IACF;;OAEG;IACH,UAAU,IAAA;IACV;;OAEG;IACH,OAAO,IAAA;IACP;;;;OAIG;IACH,aAAa,IAAA;IACb;;;;OAIG;IACH,iBAAiB,IAAA;IACjB;;OAEG;IACH,kBAAkB,KAAK;IACvB;;OAEG;IACH,YAAY,KAAA;IACZ;;OAEG;IACH,aAAa,KAAA;IACb;;;;OAIG;IACH,eAAe,KAAA;IACf;;;;OAIG;IACH,cAAc,KAAA;IACd;;OAEG;IACH,UAAU,KAAA;IACV;;;;OAIG;IACH,UAAU,KAAA;IAIV;;;;;OAKG;IACH,SAAS,IAAI;IACb;;;;OAIG;IAEH,eAAe,KAAK;IACpB;;;;OAIG;IACH,iBAAiB,KAAK;IACtB;;;;OAIG;IACH,kBAAkB,KAAK;CACvB;AAED,oBAAY,gBAAgB;IAC3B;;OAEG;IACH,IAAI,IAAI;IACR;;OAEG;IACH,IAAI,IAAA;CACJ;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IAClC;;OAEG;IACH,UAAU,EAAE,SAAS,CAAC;IACtB;;OAEG;IACH,UAAU,EAAE,SAAS,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC5B;;OAEG;IACH,EAAE,EAAE,SAAS,CAAC;IACd;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;IACpB;;;;;OAKG;IACH,KAAK,EAAE,WAAW,CAAC;IACnB;;;;;OAKG;IACH,IAAI,EAAE,WAAW,CAAC;CAClB;AAED,oBAAY,aAAa;IACxB,IAAI,IAAA;IACJ,MAAM,IAAA;CACN;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IACjC;;OAEG;IACH,QAAQ,EAAE,OAAO,CAAC;IAClB;;OAEG;IACH,qBAAqB,EAAE,yBAAyB,CAAC;IACjD;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAC1B;;OAEG;IACH,MAAM,EAAE,OAAO,CAAC;IAChB;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED,oBAAY,yBAAyB;IACpC,OAAO,KAAK;IACZ,MAAM,OAAQ;IACd,SAAS,OAAQ;IACjB,OAAO,QAAS;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC/B;;;;OAIG;IACH,EAAE,CAAC,EAAE,SAAS,CAAC;IACf;;;;OAIG;IACH,OAAO,CAAC,EAAE,SAAS,CAAC;IACpB;;OAEG;IACH,cAAc,EAAE,MAAM,CAAC;IACvB;;;;OAIG;IACH,KAAK,EAAE,iBAAiB,CAAC;IACzB;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,cAAc,CAAC;CACxB;AAED,oBAAY,iBAAiB;IAC5B;;OAEG;IACH,aAAa,IAAS;IACtB;;OAEG;IACH,WAAW,IAAS;IACpB;;OAEG;IACH,YAAY,IAAS;IACrB;;OAEG;IACH,UAAU,IAAS;CACnB;AAED,MAAM,WAAW,aAAa;IAC7B;;OAEG;IACH,OAAO,EAAE,UAAU,EAAE,CAAC;IACtB;;OAEG;IACH,OAAO,EAAE,eAAe,EAAE,CAAC;CAC3B;AAED;;GAEG;AACH,oBAAY,YAAY;IACvB;;OAEG;IACH,gBAAgB,IAAS;IACzB;;OAEG;IACH,MAAM,IAAS;IACf;;OAEG;IACH,qBAAqB,IAAS;IAC9B;;;OAGG;IACH,UAAU,KAAS;IACnB;;OAEG;IACH,MAAM,KAAS;IACf;;OAEG;IACH,sBAAsB,MAAS;IAC/B;;OAEG;IACH,OAAO,MAAS;IAChB;;OAEG;IACH,sBAAsB,MAAS;IAC/B;;OAEG;IACH,wBAAwB,QAAU;CAClC"}